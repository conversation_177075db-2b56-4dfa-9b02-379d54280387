#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 开始设置 Vue3 个人博客项目...\n')

// 检查 Node.js 版本
const nodeVersion = process.version
const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])

if (majorVersion < 16) {
  console.error('❌ 需要 Node.js 16 或更高版本')
  console.error(`当前版本: ${nodeVersion}`)
  process.exit(1)
}

console.log(`✅ Node.js 版本检查通过: ${nodeVersion}`)

// 检查是否已安装依赖
if (!fs.existsSync('node_modules')) {
  console.log('📦 安装项目依赖...')
  try {
    execSync('npm install', { stdio: 'inherit' })
    console.log('✅ 依赖安装完成')
  } catch (error) {
    console.error('❌ 依赖安装失败')
    process.exit(1)
  }
} else {
  console.log('✅ 依赖已安装')
}

// 创建环境变量文件
const envContent = `# API 基础地址
VITE_API_BASE_URL=http://localhost:8080/api

# 应用标题
VITE_APP_TITLE=个人博客

# 开发模式
VITE_NODE_ENV=development
`

if (!fs.existsSync('.env.local')) {
  fs.writeFileSync('.env.local', envContent)
  console.log('✅ 创建环境变量文件 .env.local')
} else {
  console.log('✅ 环境变量文件已存在')
}

// 创建必要的目录
const directories = [
  'public/images',
  'src/assets/images'
]

directories.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true })
    console.log(`✅ 创建目录: ${dir}`)
  }
})

console.log('\n🎉 项目设置完成！')
console.log('\n📝 接下来的步骤:')
console.log('1. 启动开发服务器: npm run dev')
console.log('2. 访问 http://localhost:3000')
console.log('3. 查看文档: npm run docs:dev')
console.log('4. 构建生产版本: npm run build')
console.log('\n📚 更多信息请查看 README.md 文件')
console.log('🔗 API 文档: docs/api/')
console.log('🔗 组件文档: docs/components/')
console.log('🔗 部署指南: docs/deployment/')

console.log('\n⚠️  注意事项:')
console.log('- 请确保后端 API 服务运行在 http://localhost:8080')
console.log('- 替换 public/avatar.jpg 为实际头像图片')
console.log('- 替换 public/default-cover.jpg 为默认封面图片')
console.log('- 在 src/views/About.vue 中更新个人信息')
