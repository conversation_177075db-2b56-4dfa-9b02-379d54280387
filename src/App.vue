<template>
  <div id="app">
    <ImageBackground />
    <router-view />
  </div>
</template>

<script setup lang="ts">
import ImageBackground from '@/components/layout/ImageBackground.vue'
// App组件作为根组件，只负责渲染路由视图
</script>

<style>
#app {
  min-height: 100vh;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
  position: relative;
  z-index: 1;
}

/* 确保内容在图片背景之上 */
#app > *:not(.image-background) {
  position: relative;
  z-index: 2;
}
</style>
