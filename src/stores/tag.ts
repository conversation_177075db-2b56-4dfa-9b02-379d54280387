import { defineStore } from 'pinia'
import { tagApi } from '@/api/tag'
import type { Tag } from '@/types'

export const useTagStore = defineStore('tag', () => {
  // 状态
  const tags = ref<Tag[]>([])
  const hotTags = ref<Tag[]>([])
  const currentTag = ref<Tag | null>(null)
  const loading = ref(false)

  // 获取所有标签
  const fetchTags = async () => {
    loading.value = true
    try {
      const response = await tagApi.getTags()
      tags.value = response.data
    } catch (error) {
      console.error('获取标签列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取热门标签
  const fetchHotTags = async (limit: number = 10) => {
    try {
      const response = await tagApi.getHotTags(limit)
      hotTags.value = response.data
    } catch (error) {
      console.error('获取热门标签失败:', error)
    }
  }

  // 获取标签详情
  const fetchTagById = async (id: number) => {
    loading.value = true
    try {
      const response = await tagApi.getTagById(id)
      currentTag.value = response.data
    } catch (error) {
      console.error('获取标签详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    tags,
    hotTags,
    currentTag,
    loading,
    fetchTags,
    fetchHotTags,
    fetchTagById
  }
})
