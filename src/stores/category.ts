import { defineStore } from 'pinia'
import { ref } from 'vue'
import { categoryApi } from '@/api/category'
import type { Category } from '@/types'

export const useCategoryStore = defineStore('category', () => {
  // 状态
  const categories = ref<Category[]>([])
  const currentCategory = ref<Category | null>(null)
  const loading = ref(false)

  // 默认分类数据
  const defaultCategories: Category[] = [
    {
      id: 1,
      name: 'Java',
      description: 'Java编程技术相关文章',
      articleCount: 0,
      createdAt: new Date().toISOString()
    },
    {
      id: 2,
      name: '网络',
      description: '网络技术相关文章',
      articleCount: 0,
      createdAt: new Date().toISOString()
    }
  ]

  // 获取所有分类
  const fetchCategories = async () => {
    loading.value = true
    try {
      const response = await categoryApi.getCategories()
      categories.value = response.data
    } catch (error) {
      console.error('获取分类列表失败:', error)
      // 如果API调用失败，使用默认分类
      categories.value = defaultCategories
    } finally {
      loading.value = false
    }
  }

  // 获取分类详情
  const fetchCategoryById = async (id: number) => {
    loading.value = true
    try {
      const response = await categoryApi.getCategoryById(id)
      currentCategory.value = response.data
    } catch (error) {
      console.error('获取分类详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 根据ID获取分类名称
  const getCategoryNameById = (id: number): string => {
    const category = categories.value.find(cat => cat.id === id)
    return category?.name || '未知分类'
  }

  return {
    categories,
    currentCategory,
    loading,
    fetchCategories,
    fetchCategoryById,
    getCategoryNameById
  }
})
