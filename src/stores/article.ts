import { defineStore } from 'pinia'
import { ref } from 'vue'
import { articleApi } from '@/api/article'
import type { Article, ArticleListResponse, SearchParams } from '@/types'

export const useArticleStore = defineStore('article', () => {
  // 状态
  const articles = ref<Article[]>([])
  const currentArticle = ref<Article | null>(null)
  const hotArticles = ref<Article[]>([])
  const latestArticles = ref<Article[]>([])
  const loading = ref(false)
  const pagination = ref({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0
  })

  // 获取文章列表
  const fetchArticles = async (params: {
    page: number
    pageSize: number
    categoryId?: number
    tagId?: number
  }) => {
    loading.value = true
    try {
      const response = await articleApi.getArticles(params)
      articles.value = response.data.articles
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('获取文章列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取文章详情
  const fetchArticleById = async (id: number) => {
    loading.value = true
    try {
      const response = await articleApi.getArticleById(id)
      currentArticle.value = response.data
      // 增加浏览量
      await articleApi.incrementViewCount(id)
    } catch (error) {
      console.error('获取文章详情失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 搜索文章
  const searchArticles = async (params: SearchParams) => {
    loading.value = true
    try {
      const response = await articleApi.searchArticles(params)
      articles.value = response.data.articles
      pagination.value = response.data.pagination
    } catch (error) {
      console.error('搜索文章失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 获取热门文章
  const fetchHotArticles = async (limit: number = 5) => {
    try {
      const response = await articleApi.getHotArticles(limit)
      hotArticles.value = response.data
    } catch (error) {
      console.error('获取热门文章失败:', error)
    }
  }

  // 获取最新文章
  const fetchLatestArticles = async (limit: number = 5) => {
    try {
      const response = await articleApi.getLatestArticles(limit)
      latestArticles.value = response.data
    } catch (error) {
      console.error('获取最新文章失败:', error)
    }
  }

  // 点赞文章
  const likeArticle = async (id: number) => {
    try {
      await articleApi.likeArticle(id)
      if (currentArticle.value && currentArticle.value.id === id) {
        currentArticle.value.likeCount++
      }
      ElMessage.success('点赞成功')
    } catch (error) {
      console.error('点赞失败:', error)
    }
  }

  return {
    articles,
    currentArticle,
    hotArticles,
    latestArticles,
    loading,
    pagination,
    fetchArticles,
    fetchArticleById,
    searchArticles,
    fetchHotArticles,
    fetchLatestArticles,
    likeArticle
  }
})
