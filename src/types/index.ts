// 文章类型定义
export interface Article {
  id: number
  title: string
  content: string
  summary: string
  author: string
  categoryId: number
  categoryName: string
  tags: Tag[]
  coverImage?: string
  viewCount: number
  likeCount: number
  commentCount: number
  createdAt: string
  updatedAt: string
  isPublished: boolean
}

// 分类类型定义
export interface Category {
  id: number
  name: string
  description: string
  articleCount: number
  createdAt: string
}

// 标签类型定义
export interface Tag {
  id: number
  name: string
  color: string
  articleCount: number
  createdAt: string
}

// 评论类型定义
export interface Comment {
  id: number
  articleId: number
  content: string
  author: string
  email: string
  website?: string
  parentId?: number
  children?: Comment[]
  createdAt: string
  isApproved: boolean
}

// 分页参数
export interface PaginationParams {
  page: number
  pageSize: number
  total?: number
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 文章列表响应
export interface ArticleListResponse {
  articles: Article[]
  pagination: {
    page: number
    pageSize: number
    total: number
    totalPages: number
  }
}

// 搜索参数
export interface SearchParams {
  keyword: string
  categoryId?: number
  tagId?: number
  page: number
  pageSize: number
}
