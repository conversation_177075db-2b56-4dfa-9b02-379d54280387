<template>
  <div class="welcome-banner fade-in">
    <div class="banner-content">
      <h1 class="banner-title">
        欢迎来到我的
        <span class="accent-red">个人博客</span>
      </h1>
      <p class="banner-subtitle">
        探索技术的无限可能，分享编程的精彩世界
      </p>
      <div class="banner-stats">
        <div class="stat-item pulse-red">
          <div class="stat-number">{{ totalArticles }}</div>
          <div class="stat-label">精彩文章</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ totalViews }}</div>
          <div class="stat-label">总访问量</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ totalCategories }}</div>
          <div class="stat-label">技术分类</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCategoryStore } from '@/stores/category'

const categoryStore = useCategoryStore()

// 计算属性
const totalArticles = computed(() => {
  return categoryStore.categories.reduce((total, category) => total + category.articleCount, 0) || 42
})

const totalViews = computed(() => '10.5K')
const totalCategories = computed(() => categoryStore.categories.length || 8)

// 生命周期
onMounted(() => {
  categoryStore.fetchCategories()
})
</script>

<style scoped>
.welcome-banner {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 60px 40px;
  margin-bottom: 40px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.welcome-banner::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(220, 20, 60, 0.1) 0%, transparent 70%);
  animation: rotate 20s linear infinite;
  z-index: -1;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.banner-content {
  position: relative;
  z-index: 1;
}

.banner-title {
  font-size: 48px;
  font-weight: bold;
  margin: 0 0 20px;
  color: #fff;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.banner-subtitle {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0 0 40px;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

.banner-stats {
  display: flex;
  justify-content: center;
  gap: 60px;
}

.stat-item {
  text-align: center;
  color: #fff;
}

.stat-number {
  font-size: 36px;
  font-weight: bold;
  margin-bottom: 8px;
  text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
  text-shadow: 0 1px 5px rgba(0, 0, 0, 0.3);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-banner {
    padding: 40px 20px;
    margin-bottom: 30px;
  }
  
  .banner-title {
    font-size: 32px;
  }
  
  .banner-subtitle {
    font-size: 16px;
  }
  
  .banner-stats {
    flex-direction: column;
    gap: 30px;
  }
  
  .stat-number {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .banner-title {
    font-size: 24px;
  }
  
  .banner-subtitle {
    font-size: 14px;
  }
  
  .stat-number {
    font-size: 24px;
  }
}
</style>
