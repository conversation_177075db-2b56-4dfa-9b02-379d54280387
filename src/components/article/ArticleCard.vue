<template>
  <div class="article-card" @click="$emit('click')">
    <div class="card-content">
      <!-- 封面图片 -->
      <div v-if="article.coverImage" class="cover-image">
        <img :src="article.coverImage" :alt="article.title" />
      </div>
      
      <!-- 文章信息 -->
      <div class="article-info">
        <h3 class="article-title">{{ article.title }}</h3>
        <p class="article-summary">{{ article.summary }}</p>
        
        <!-- 标签 -->
        <div class="article-tags">
          <el-tag
            v-for="tag in article.tags"
            :key="tag.id"
            :color="tag.color"
            size="small"
            class="tag-item"
          >
            {{ tag.name }}
          </el-tag>
        </div>
        
        <!-- 元信息 -->
        <div class="article-meta">
          <div class="meta-left">
            <span class="category">
              <el-icon><Folder /></el-icon>
              {{ article.categoryName }}
            </span>
            <span class="author">
              <el-icon><User /></el-icon>
              {{ article.author }}
            </span>
            <span class="date">
              <el-icon><Calendar /></el-icon>
              {{ formatDate(article.createdAt) }}
            </span>
          </div>
          <div class="meta-right">
            <span class="view-count">
              <el-icon><View /></el-icon>
              {{ article.viewCount }}
            </span>
            <span class="like-count">
              <el-icon><Star /></el-icon>
              {{ article.likeCount }}
            </span>
            <span class="comment-count">
              <el-icon><ChatDotRound /></el-icon>
              {{ article.commentCount }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Folder, User, Calendar, View, Star, ChatDotRound } from '@element-plus/icons-vue'
import type { Article } from '@/types'
import dayjs from 'dayjs'

interface Props {
  article: Article
}

defineProps<Props>()
defineEmits<{
  click: []
}>()

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD')
}
</script>

<style scoped>
.article-card {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  overflow: hidden;
}

.article-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.95);
}

.card-content {
  display: flex;
  gap: 20px;
  padding: 20px;
}

.cover-image {
  flex-shrink: 0;
  width: 200px;
  height: 150px;
  border-radius: 6px;
  overflow: hidden;
}

.cover-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.article-card:hover .cover-image img {
  transform: scale(1.05);
}

.article-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.article-title {
  margin: 0;
  font-size: 18px;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-summary {
  margin: 0;
  color: #666;
  line-height: 1.6;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 13px;
  color: #999;
  margin-top: auto;
}

.meta-left,
.meta-right {
  display: flex;
  gap: 15px;
}

.meta-left span,
.meta-right span {
  display: flex;
  align-items: center;
  gap: 4px;
}

.meta-right span {
  color: #666;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    flex-direction: column;
    gap: 15px;
  }
  
  .cover-image {
    width: 100%;
    height: 200px;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .meta-left,
  .meta-right {
    justify-content: space-between;
  }
}

@media (max-width: 480px) {
  .card-content {
    padding: 15px;
  }
  
  .meta-left,
  .meta-right {
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
