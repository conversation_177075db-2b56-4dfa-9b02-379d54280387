<template>
  <div class="comment-item">
    <div class="comment-header">
      <div class="comment-author">
        <strong>{{ comment.author }}</strong>
        <span v-if="comment.website" class="author-website">
          <a :href="comment.website" target="_blank" rel="noopener noreferrer">
            <el-icon><Link /></el-icon>
          </a>
        </span>
      </div>
      <div class="comment-time">
        {{ formatTime(comment.createdAt) }}
      </div>
    </div>
    
    <div class="comment-content">
      {{ comment.content }}
    </div>
    
    <div class="comment-actions">
      <el-button
        type="text"
        size="small"
        @click="$emit('reply', comment)"
      >
        <el-icon><ChatDotRound /></el-icon>
        回复
      </el-button>
    </div>
    
    <!-- 子评论 -->
    <div v-if="comment.children && comment.children.length > 0" class="comment-children">
      <CommentItem
        v-for="child in comment.children"
        :key="child.id"
        :comment="child"
        @reply="$emit('reply', $event)"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { Link, ChatDotRound } from '@element-plus/icons-vue'
import type { Comment } from '@/types'
import dayjs from 'dayjs'

interface Props {
  comment: Comment
}

defineProps<Props>()
defineEmits<{
  reply: [comment: Comment]
}>()

const formatTime = (time: string) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm')
}
</script>

<style scoped>
.comment-item {
  padding: 15px 0;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.comment-author {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #333;
  font-size: 14px;
}

.author-website a {
  color: #409eff;
  text-decoration: none;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  margin-bottom: 10px;
  line-height: 1.6;
  color: #333;
  white-space: pre-wrap;
}

.comment-actions {
  display: flex;
  gap: 10px;
}

.comment-children {
  margin-left: 30px;
  margin-top: 15px;
  padding-left: 20px;
  border-left: 2px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-children {
    margin-left: 15px;
    padding-left: 15px;
  }
}
</style>
