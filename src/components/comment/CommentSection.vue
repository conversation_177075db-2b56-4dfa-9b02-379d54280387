<template>
  <div class="comment-section">
    <div class="comment-container">
      <h3 class="comment-title">
        评论 ({{ comments.length }})
      </h3>

      <!-- 评论表单 -->
      <div class="comment-form">
        <el-form
          ref="commentFormRef"
          :model="commentForm"
          :rules="commentRules"
          label-width="80px"
        >
          <el-form-item label="昵称" prop="author">
            <el-input
              v-model="commentForm.author"
              placeholder="请输入您的昵称"
              maxlength="20"
            />
          </el-form-item>
          
          <el-form-item label="邮箱" prop="email">
            <el-input
              v-model="commentForm.email"
              placeholder="请输入您的邮箱（不会公开）"
              type="email"
            />
          </el-form-item>
          
          <el-form-item label="网站" prop="website">
            <el-input
              v-model="commentForm.website"
              placeholder="您的网站（可选）"
            />
          </el-form-item>
          
          <el-form-item label="评论" prop="content">
            <el-input
              v-model="commentForm.content"
              type="textarea"
              :rows="4"
              placeholder="请输入您的评论..."
              maxlength="500"
              show-word-limit
            />
          </el-form-item>
          
          <el-form-item>
            <el-button
              type="primary"
              @click="submitComment"
              :loading="submitting"
            >
              发表评论
            </el-button>
            <el-button @click="resetForm">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 评论列表 -->
      <div v-loading="loading" class="comment-list">
        <div
          v-for="comment in comments"
          :key="comment.id"
          class="comment-item"
        >
          <CommentItem
            :comment="comment"
            @reply="handleReply"
          />
        </div>
        
        <div v-if="comments.length === 0 && !loading" class="no-comments">
          <el-empty description="暂无评论，快来发表第一条评论吧！" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { commentApi } from '@/api/comment'
import CommentItem from './CommentItem.vue'
import type { Comment } from '@/types'
import type { FormInstance, FormRules } from 'element-plus'

interface Props {
  articleId: number
}

const props = defineProps<Props>()

// 响应式数据
const comments = ref<Comment[]>([])
const loading = ref(false)
const submitting = ref(false)
const commentFormRef = ref<FormInstance>()

const commentForm = ref({
  author: '',
  email: '',
  website: '',
  content: '',
  parentId: undefined as number | undefined
})

// 表单验证规则
const commentRules: FormRules = {
  author: [
    { required: true, message: '请输入昵称', trigger: 'blur' },
    { min: 2, max: 20, message: '昵称长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  email: [
    { required: true, message: '请输入邮箱', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
  ],
  content: [
    { required: true, message: '请输入评论内容', trigger: 'blur' },
    { min: 5, max: 500, message: '评论长度在 5 到 500 个字符', trigger: 'blur' }
  ]
}

// 方法
const fetchComments = async () => {
  loading.value = true
  try {
    const response = await commentApi.getCommentsByArticleId(props.articleId)
    comments.value = response.data
  } catch (error) {
    console.error('获取评论失败:', error)
  } finally {
    loading.value = false
  }
}

const submitComment = async () => {
  if (!commentFormRef.value) return
  
  const valid = await commentFormRef.value.validate()
  if (!valid) return
  
  submitting.value = true
  try {
    await commentApi.addComment({
      articleId: props.articleId,
      ...commentForm.value
    })
    
    ElMessage.success('评论发表成功')
    resetForm()
    fetchComments()
  } catch (error) {
    console.error('发表评论失败:', error)
  } finally {
    submitting.value = false
  }
}

const resetForm = () => {
  commentFormRef.value?.resetFields()
  commentForm.value.parentId = undefined
}

const handleReply = (comment: Comment) => {
  commentForm.value.parentId = comment.id
  // 滚动到评论表单
  document.querySelector('.comment-form')?.scrollIntoView({ behavior: 'smooth' })
}

// 生命周期
onMounted(() => {
  fetchComments()
})
</script>

<style scoped>
.comment-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.comment-title {
  margin: 0 0 30px;
  font-size: 20px;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.comment-form {
  margin-bottom: 40px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.comment-item {
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 20px;
}

.comment-item:last-child {
  border-bottom: none;
  padding-bottom: 0;
}

.no-comments {
  text-align: center;
  padding: 40px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .comment-section {
    padding: 20px;
  }
  
  .comment-form {
    padding: 15px;
  }
}
</style>
