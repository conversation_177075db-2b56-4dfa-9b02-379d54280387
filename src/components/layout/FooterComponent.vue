<template>
  <footer class="footer">
    <div class="container">
      <div class="footer-content">
        <div class="footer-info">
          <p>&copy; {{ currentYear }} 个人博客. All rights reserved.</p>
          <p>Powered by Vue 3 + TypeScript</p>
        </div>
        <div class="footer-links">
          <a href="https://github.com" target="_blank" rel="noopener noreferrer">
            <el-icon><Link /></el-icon>
            GitHub
          </a>
          <a href="mailto:<EMAIL>">
            <el-icon><Message /></el-icon>
            Email
          </a>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
import { Link, Message } from '@element-plus/icons-vue'

const currentYear = new Date().getFullYear()
</script>

<style scoped>
.footer {
  background-color: #2c3e50;
  color: #ecf0f1;
  padding: 30px 0;
  margin-top: auto;
}

.footer-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.footer-info p {
  margin: 0;
  font-size: 14px;
  line-height: 1.5;
}

.footer-links {
  display: flex;
  gap: 20px;
}

.footer-links a {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #ecf0f1;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.footer-links a:hover {
  color: #3498db;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
}
</style>
