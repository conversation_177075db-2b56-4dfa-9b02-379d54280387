<template>
  <div class="image-background">
    <div class="background-image"></div>
    <div class="image-overlay"></div>
  </div>
</template>

<script setup lang="ts">
// 图片背景组件
</script>

<style scoped>
.image-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}

.background-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('/images/eye-background.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  filter: brightness(0.3) contrast(1.2);
  transition: filter 0.3s ease;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.4);
  z-index: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .background-image {
    filter: brightness(0.2) contrast(1.1);
  }
  
  .image-overlay {
    background: rgba(0, 0, 0, 0.5);
  }
}
</style> 