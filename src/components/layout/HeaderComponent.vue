<template>
  <header class="header">
    <div class="container">
      <div class="header-content">
        <!-- Logo -->
        <div class="logo">
          <router-link to="/" class="logo-link">
            <h1>个人博客</h1>
          </router-link>
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav">
          <el-menu
            :default-active="activeIndex"
            mode="horizontal"
            @select="handleSelect"
            class="nav-menu"
          >
            <el-menu-item index="/">首页</el-menu-item>
            <el-sub-menu index="categories">
              <template #title>分类</template>
              <el-menu-item
                v-for="category in categories"
                :key="category.id"
                :index="`/category/${category.id}`"
              >
                {{ category.name }}
              </el-menu-item>
            </el-sub-menu>
            <el-menu-item index="/about">关于</el-menu-item>
          </el-menu>
        </nav>
        
        <!-- 搜索框 -->
        <div class="search">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索文章..."
            @keyup.enter="handleSearch"
            class="search-input"
          >
            <template #suffix>
              <el-icon @click="handleSearch" class="search-icon">
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>
        
        <!-- 移动端菜单按钮 -->
        <div class="mobile-menu-btn" @click="toggleMobileMenu">
          <el-icon><Menu /></el-icon>
        </div>
      </div>
    </div>
    
    <!-- 移动端菜单 -->
    <div v-show="showMobileMenu" class="mobile-menu">
      <div class="mobile-menu-content">
        <router-link to="/" @click="closeMobileMenu">首页</router-link>
        <div class="mobile-categories">
          <div class="mobile-category-title">分类</div>
          <router-link
            v-for="category in categories"
            :key="category.id"
            :to="`/category/${category.id}`"
            @click="closeMobileMenu"
            class="mobile-category-item"
          >
            {{ category.name }}
          </router-link>
        </div>
        <router-link to="/about" @click="closeMobileMenu">关于</router-link>
      </div>
    </div>
  </header>
</template>

<script setup lang="ts">
import { Search, Menu } from '@element-plus/icons-vue'
import { useCategoryStore } from '@/stores/category'

const router = useRouter()
const route = useRoute()
const categoryStore = useCategoryStore()

// 响应式数据
const searchKeyword = ref('')
const showMobileMenu = ref(false)

// 计算属性
const activeIndex = computed(() => route.path)
const categories = computed(() => categoryStore.categories)

// 方法
const handleSelect = (index: string) => {
  router.push(index)
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/search',
      query: { keyword: searchKeyword.value.trim() }
    })
    searchKeyword.value = ''
  }
}

const toggleMobileMenu = () => {
  showMobileMenu.value = !showMobileMenu.value
}

const closeMobileMenu = () => {
  showMobileMenu.value = false
}

// 生命周期
onMounted(() => {
  categoryStore.fetchCategories()
})
</script>

<style scoped>
.header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
}

.logo-link {
  color: #333;
  text-decoration: none;
}

.logo h1 {
  margin: 0;
  font-size: 24px;
  font-weight: bold;
  color: #409eff;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.nav {
  flex: 1;
  margin: 0 30px;
}

.nav-menu {
  border-bottom: none;
  background: transparent;
}

.search {
  width: 250px;
}

.search-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.search-icon {
  cursor: pointer;
  color: #999;
}

.search-icon:hover {
  color: #409eff;
}

.mobile-menu-btn {
  display: none;
  cursor: pointer;
  font-size: 20px;
  color: #333;
}

.mobile-menu {
  display: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.mobile-menu-content {
  padding: 20px;
}

.mobile-menu-content a {
  display: block;
  padding: 10px 0;
  color: #333;
  text-decoration: none;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  transition: color 0.3s ease;
}

.mobile-menu-content a:hover {
  color: #409eff;
}

.mobile-categories {
  margin: 10px 0;
}

.mobile-category-title {
  font-weight: bold;
  color: #666;
  margin-bottom: 5px;
}

.mobile-category-item {
  padding-left: 20px !important;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .nav {
    display: none;
  }
  
  .search {
    width: 200px;
  }
  
  .mobile-menu-btn {
    display: block;
  }
  
  .mobile-menu {
    display: block;
  }
}

@media (max-width: 480px) {
  .search {
    width: 150px;
  }
  
  .logo h1 {
    font-size: 20px;
  }
}
</style>
