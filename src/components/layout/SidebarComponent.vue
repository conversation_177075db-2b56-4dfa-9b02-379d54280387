<template>
  <div class="sidebar">
    <!-- 个人信息卡片 -->
    <el-card class="sidebar-card">
      <div class="profile">
        <div class="avatar">
          <img src="/avatar.jpg" alt="头像" />
        </div>
        <h3 class="name">博主姓名</h3>
        <p class="description">这里是个人简介，可以写一些关于自己的介绍...</p>
        <div class="stats">
          <div class="stat-item">
            <span class="stat-number">{{ totalArticles }}</span>
            <span class="stat-label">文章</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ totalCategories }}</span>
            <span class="stat-label">分类</span>
          </div>
          <div class="stat-item">
            <span class="stat-number">{{ totalTags }}</span>
            <span class="stat-label">标签</span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 热门文章 -->
    <el-card class="sidebar-card">
      <template #header>
        <div class="card-header">
          <el-icon><Star /></el-icon>
          <span>热门文章</span>
        </div>
      </template>
      <div v-loading="articleStore.loading" class="hot-articles">
        <div
          v-for="article in hotArticles"
          :key="article.id"
          class="hot-article-item"
          @click="goToArticle(article.id)"
        >
          <h4 class="article-title">{{ article.title }}</h4>
          <div class="article-meta">
            <span class="view-count">
              <el-icon><View /></el-icon>
              {{ article.viewCount }}
            </span>
            <span class="like-count">
              <el-icon><Star /></el-icon>
              {{ article.likeCount }}
            </span>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 热门标签 -->
    <el-card class="sidebar-card">
      <template #header>
        <div class="card-header">
          <el-icon><Collection /></el-icon>
          <span>热门标签</span>
        </div>
      </template>
      <div v-loading="tagStore.loading" class="hot-tags">
        <el-tag
          v-for="tag in hotTags"
          :key="tag.id"
          :color="tag.color"
          class="tag-item"
          @click="goToTag(tag.id)"
        >
          {{ tag.name }} ({{ tag.articleCount }})
        </el-tag>
      </div>
    </el-card>

    <!-- 最新评论 -->
    <el-card class="sidebar-card">
      <template #header>
        <div class="card-header">
          <el-icon><ChatDotRound /></el-icon>
          <span>最新评论</span>
        </div>
      </template>
      <div class="latest-comments">
        <div
          v-for="comment in latestComments"
          :key="comment.id"
          class="comment-item"
        >
          <div class="comment-author">{{ comment.author }}</div>
          <div class="comment-content">{{ comment.content }}</div>
          <div class="comment-time">{{ formatTime(comment.createdAt) }}</div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { Star, View, Collection, ChatDotRound } from '@element-plus/icons-vue'
import { useArticleStore } from '@/stores/article'
import { useCategoryStore } from '@/stores/category'
import { useTagStore } from '@/stores/tag'
import { commentApi } from '@/api/comment'
import type { Comment } from '@/types'
import dayjs from 'dayjs'

const router = useRouter()
const articleStore = useArticleStore()
const categoryStore = useCategoryStore()
const tagStore = useTagStore()

// 响应式数据
const latestComments = ref<Comment[]>([])

// 计算属性
const hotArticles = computed(() => articleStore.hotArticles)
const hotTags = computed(() => tagStore.hotTags)
const totalArticles = computed(() => {
  return categoryStore.categories.reduce((total, category) => total + category.articleCount, 0)
})
const totalCategories = computed(() => categoryStore.categories.length)
const totalTags = computed(() => tagStore.tags.length)

// 方法
const goToArticle = (id: number) => {
  router.push(`/article/${id}`)
}

const goToTag = (id: number) => {
  router.push(`/tag/${id}`)
}

const formatTime = (time: string) => {
  return dayjs(time).format('MM-DD HH:mm')
}

const fetchLatestComments = async () => {
  try {
    const response = await commentApi.getLatestComments(5)
    latestComments.value = response.data
  } catch (error) {
    console.error('获取最新评论失败:', error)
  }
}

// 生命周期
onMounted(() => {
  articleStore.fetchHotArticles(5)
  tagStore.fetchHotTags(10)
  fetchLatestComments()
})
</script>

<style scoped>
.sidebar {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #333;
}

/* 个人信息样式 */
.profile {
  text-align: center;
}

.avatar {
  width: 80px;
  height: 80px;
  margin: 0 auto 15px;
  border-radius: 50%;
  overflow: hidden;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.name {
  margin: 0 0 10px;
  font-size: 18px;
  color: #333;
}

.description {
  margin: 0 0 20px;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
}

.stats {
  display: flex;
  justify-content: space-around;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-number {
  font-size: 20px;
  font-weight: bold;
  color: #409eff;
}

.stat-label {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

/* 热门文章样式 */
.hot-articles {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.hot-article-item {
  cursor: pointer;
  padding: 10px;
  border-radius: 6px;
  transition: background-color 0.3s ease;
}

.hot-article-item:hover {
  background-color: #f8f9fa;
}

.article-title {
  margin: 0 0 8px;
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-meta {
  display: flex;
  gap: 15px;
  font-size: 12px;
  color: #999;
}

.view-count,
.like-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 热门标签样式 */
.hot-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tag-item {
  cursor: pointer;
  transition: transform 0.2s ease;
}

.tag-item:hover {
  transform: translateY(-2px);
}

/* 最新评论样式 */
.latest-comments {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.comment-item {
  padding: 10px;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.comment-author {
  font-size: 12px;
  color: #409eff;
  font-weight: bold;
  margin-bottom: 5px;
}

.comment-content {
  font-size: 13px;
  color: #333;
  line-height: 1.4;
  margin-bottom: 5px;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.comment-time {
  font-size: 11px;
  color: #999;
}
</style>
