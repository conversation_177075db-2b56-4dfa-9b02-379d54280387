<template>
  <div class="layout">
    <!-- 头部 -->
    <HeaderComponent />
    
    <!-- 主体内容 -->
    <div class="main-container">
      <div class="container">
        <div class="content-wrapper">
          <!-- 主要内容区域 -->
          <main class="main-content fade-in">
            <router-view />
          </main>
          
          <!-- 侧边栏 -->
          <aside class="sidebar fade-in floating">
            <SidebarComponent />
          </aside>
        </div>
      </div>
    </div>
    
    <!-- 底部 -->
    <FooterComponent />
  </div>
</template>

<script setup lang="ts">
import HeaderComponent from '@/components/layout/HeaderComponent.vue'
import SidebarComponent from '@/components/layout/SidebarComponent.vue'
import FooterComponent from '@/components/layout/FooterComponent.vue'
</script>

<style scoped>
.layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.main-container {
  flex: 1;
  padding: 20px 0;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
}

.content-wrapper {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 30px;
  align-items: start;
}

.main-content {
  min-height: 500px;
}

.sidebar {
  position: sticky;
  top: 20px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-wrapper {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .sidebar {
    position: static;
    order: -1;
  }
}
</style>
