import api from './index'
import type { Tag, ApiResponse } from '@/types'
import { mockTags } from './mockData'

// 标签相关API
export const tagApi = {
  // 获取所有标签
  getTags(): Promise<ApiResponse<Tag[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          code: 200,
          message: '获取标签列表成功',
          data: mockTags
        })
      }, 100)
    })
  },

  // 获取热门标签
  getHotTags(limit: number = 10): Promise<ApiResponse<Tag[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const hotTags = [...mockTags]
          .sort((a, b) => b.articleCount - a.articleCount)
          .slice(0, limit)
        
        resolve({
          code: 200,
          message: '获取热门标签成功',
          data: hotTags
        })
      }, 100)
    })
  },

  // 获取标签详情
  getTagById(id: number): Promise<ApiResponse<Tag>> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const tag = mockTags.find(t => t.id === id)
        if (tag) {
          resolve({
            code: 200,
            message: '获取标签详情成功',
            data: tag
          })
        } else {
          reject(new Error('标签不存在'))
        }
      }, 100)
    })
  }
}
