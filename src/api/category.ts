import api from './index'
import type { Category, ApiResponse } from '@/types'
import { mockCategories } from './mockData'

// 分类相关API
export const categoryApi = {
  // 获取所有分类
  getCategories(): Promise<ApiResponse<Category[]>> {
    return new Promise((resolve) => {
      // 模拟API调用延迟
      setTimeout(() => {
        resolve({
          code: 200,
          message: '获取分类成功',
          data: mockCategories
        })
      }, 100)
    })
  },

  // 获取分类详情
  getCategoryById(id: number): Promise<ApiResponse<Category>> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const category = mockCategories.find(cat => cat.id === id)
        if (category) {
          resolve({
            code: 200,
            message: '获取分类详情成功',
            data: category
          })
        } else {
          reject(new Error('分类不存在'))
        }
      }, 100)
    })
  }
}
