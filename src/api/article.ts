import api from './index'
import type { Article, ArticleListResponse, SearchParams, ApiResponse } from '@/types'
import { mockArticles } from './mockData'

// 文章相关API
export const articleApi = {
  // 获取文章列表
  getArticles(params: {
    page: number
    pageSize: number
    categoryId?: number
    tagId?: number
  }): Promise<ApiResponse<ArticleListResponse>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredArticles = [...mockArticles]
        
        // 按分类过滤
        if (params.categoryId) {
          filteredArticles = filteredArticles.filter(article => article.categoryId === params.categoryId)
        }
        
        // 按标签过滤
        if (params.tagId) {
          filteredArticles = filteredArticles.filter(article => 
            article.tags.some(tag => tag.id === params.tagId)
          )
        }
        
        const start = (params.page - 1) * params.pageSize
        const end = start + params.pageSize
        const articles = filteredArticles.slice(start, end)
        
        resolve({
          code: 200,
          message: '获取文章列表成功',
          data: {
            articles,
            pagination: {
              page: params.page,
              pageSize: params.pageSize,
              total: filteredArticles.length,
              totalPages: Math.ceil(filteredArticles.length / params.pageSize)
            }
          }
        })
      }, 100)
    })
  },

  // 获取文章详情
  getArticleById(id: number): Promise<ApiResponse<Article>> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const article = mockArticles.find(art => art.id === id)
        if (article) {
          resolve({
            code: 200,
            message: '获取文章详情成功',
            data: article
          })
        } else {
          reject(new Error('文章不存在'))
        }
      }, 100)
    })
  },

  // 搜索文章
  searchArticles(params: SearchParams): Promise<ApiResponse<ArticleListResponse>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        let filteredArticles = [...mockArticles]
        
        // 关键词搜索
        if (params.keyword) {
          const keyword = params.keyword.toLowerCase()
          filteredArticles = filteredArticles.filter(article => 
            article.title.toLowerCase().includes(keyword) ||
            article.content.toLowerCase().includes(keyword) ||
            article.summary.toLowerCase().includes(keyword)
          )
        }
        
        // 按分类过滤
        if (params.categoryId) {
          filteredArticles = filteredArticles.filter(article => article.categoryId === params.categoryId)
        }
        
        // 按标签过滤
        if (params.tagId) {
          filteredArticles = filteredArticles.filter(article => 
            article.tags.some(tag => tag.id === params.tagId)
          )
        }
        
        const start = (params.page - 1) * params.pageSize
        const end = start + params.pageSize
        const articles = filteredArticles.slice(start, end)
        
        resolve({
          code: 200,
          message: '搜索文章成功',
          data: {
            articles,
            pagination: {
              page: params.page,
              pageSize: params.pageSize,
              total: filteredArticles.length,
              totalPages: Math.ceil(filteredArticles.length / params.pageSize)
            }
          }
        })
      }, 100)
    })
  },

  // 获取热门文章
  getHotArticles(limit: number = 5): Promise<ApiResponse<Article[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const hotArticles = [...mockArticles]
          .sort((a, b) => b.viewCount - a.viewCount)
          .slice(0, limit)
        
        resolve({
          code: 200,
          message: '获取热门文章成功',
          data: hotArticles
        })
      }, 100)
    })
  },

  // 获取最新文章
  getLatestArticles(limit: number = 5): Promise<ApiResponse<Article[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const latestArticles = [...mockArticles]
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, limit)
        
        resolve({
          code: 200,
          message: '获取最新文章成功',
          data: latestArticles
        })
      }, 100)
    })
  },

  // 增加文章浏览量
  incrementViewCount(id: number): Promise<ApiResponse<void>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = mockArticles.find(art => art.id === id)
        if (article) {
          article.viewCount++
        }
        resolve({
          code: 200,
          message: '增加浏览量成功',
          data: undefined
        })
      }, 100)
    })
  },

  // 点赞文章
  likeArticle(id: number): Promise<ApiResponse<void>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const article = mockArticles.find(art => art.id === id)
        if (article) {
          article.likeCount++
        }
        resolve({
          code: 200,
          message: '点赞成功',
          data: undefined
        })
      }, 100)
    })
  }
}
