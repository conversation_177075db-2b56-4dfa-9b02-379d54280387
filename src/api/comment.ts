import api from './index'
import type { Comment, ApiResponse } from '@/types'

// 模拟评论数据
const mockComments: Comment[] = [
  {
    id: 1,
    articleId: 1,
    content: '这篇文章写得很好，对Spring Boot的介绍很详细！',
    author: '小明',
    email: '<EMAIL>',
    createdAt: '2024-01-15T11:30:00Z',
    isApproved: true
  },
  {
    id: 2,
    articleId: 1,
    content: '请问如何配置数据库连接？',
    author: '小红',
    email: '<EMAIL>',
    createdAt: '2024-01-15T12:15:00Z',
    isApproved: true
  },
  {
    id: 3,
    articleId: 2,
    content: 'Vue 3.0的新特性确实很强大！',
    author: '小李',
    email: '<EMAIL>',
    createdAt: '2024-01-14T15:45:00Z',
    isApproved: true
  },
  {
    id: 4,
    articleId: 3,
    content: 'HTTP协议的基础知识讲解得很清楚',
    author: '小王',
    email: '<EMAIL>',
    createdAt: '2024-01-13T10:20:00Z',
    isApproved: true
  },
  {
    id: 5,
    articleId: 4,
    content: '多线程编程确实是个难点，这篇文章帮助很大',
    author: '小张',
    email: '<EMAIL>',
    createdAt: '2024-01-12T17:30:00Z',
    isApproved: true
  }
]

// 评论相关API
export const commentApi = {
  // 获取文章评论
  getCommentsByArticleId(articleId: number): Promise<ApiResponse<Comment[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const comments = mockComments.filter(comment => comment.articleId === articleId)
        resolve({
          code: 200,
          message: '获取评论成功',
          data: comments
        })
      }, 100)
    })
  },

  // 添加评论
  addComment(comment: {
    articleId: number
    content: string
    author: string
    email: string
    website?: string
    parentId?: number
  }): Promise<ApiResponse<Comment>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newComment: Comment = {
          id: mockComments.length + 1,
          articleId: comment.articleId,
          content: comment.content,
          author: comment.author,
          email: comment.email,
          website: comment.website,
          parentId: comment.parentId,
          createdAt: new Date().toISOString(),
          isApproved: true
        }
        
        mockComments.push(newComment)
        
        resolve({
          code: 200,
          message: '添加评论成功',
          data: newComment
        })
      }, 100)
    })
  },

  // 获取最新评论
  getLatestComments(limit: number = 5): Promise<ApiResponse<Comment[]>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const latestComments = [...mockComments]
          .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
          .slice(0, limit)
        
        resolve({
          code: 200,
          message: '获取最新评论成功',
          data: latestComments
        })
      }, 100)
    })
  }
}
