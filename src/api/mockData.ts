import type { Category, Article, Tag } from '@/types'

// 模拟分类数据
export const mockCategories: Category[] = [
  {
    id: 1,
    name: 'Java',
    description: 'Java编程技术相关文章',
    articleCount: 3,
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    name: '网络',
    description: '网络技术相关文章',
    articleCount: 2,
    createdAt: new Date().toISOString()
  }
]

// 模拟标签数据
export const mockTags: Tag[] = [
  {
    id: 1,
    name: 'Spring Boot',
    color: '#67C23A',
    articleCount: 2,
    createdAt: new Date().toISOString()
  },
  {
    id: 2,
    name: 'Vue.js',
    color: '#409EFF',
    articleCount: 1,
    createdAt: new Date().toISOString()
  },
  {
    id: 3,
    name: 'HTTP',
    color: '#E6A23C',
    articleCount: 1,
    createdAt: new Date().toISOString()
  }
]

// 模拟文章数据
export const mockArticles: Article[] = [
  {
    id: 1,
    title: 'Spring Boot 入门指南',
    content: '# Spring Boot 入门指南\n\nSpring Boot 是一个基于 Spring 框架的快速开发框架...',
    summary: '本文介绍 Spring Boot 的基本概念和使用方法，帮助初学者快速上手。',
    author: '张三',
    categoryId: 1,
    categoryName: 'Java',
    tags: [mockTags[0]],
    coverImage: '/images/spring-boot.jpg',
    viewCount: 156,
    likeCount: 23,
    commentCount: 5,
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    isPublished: true
  },
  {
    id: 2,
    title: 'Vue.js 3.0 新特性解析',
    content: '# Vue.js 3.0 新特性解析\n\nVue.js 3.0 带来了许多激动人心的新特性...',
    summary: '详细介绍 Vue.js 3.0 的新特性，包括 Composition API、Teleport 等。',
    author: '李四',
    categoryId: 2,
    categoryName: '网络',
    tags: [mockTags[1]],
    coverImage: '/images/vue3.jpg',
    viewCount: 89,
    likeCount: 15,
    commentCount: 3,
    createdAt: '2024-01-14T14:30:00Z',
    updatedAt: '2024-01-14T14:30:00Z',
    isPublished: true
  },
  {
    id: 3,
    title: 'HTTP 协议详解',
    content: '# HTTP 协议详解\n\nHTTP（超文本传输协议）是互联网上应用最为广泛的协议...',
    summary: '深入解析 HTTP 协议的工作原理、请求方法、状态码等核心概念。',
    author: '王五',
    categoryId: 2,
    categoryName: '网络',
    tags: [mockTags[2]],
    coverImage: '/images/http.jpg',
    viewCount: 234,
    likeCount: 45,
    commentCount: 8,
    createdAt: '2024-01-13T09:15:00Z',
    updatedAt: '2024-01-13T09:15:00Z',
    isPublished: true
  },
  {
    id: 4,
    title: 'Java 多线程编程实践',
    content: '# Java 多线程编程实践\n\n多线程是 Java 编程中的重要概念...',
    summary: '通过实际案例学习 Java 多线程编程，包括线程创建、同步、通信等。',
    author: '赵六',
    categoryId: 1,
    categoryName: 'Java',
    tags: [mockTags[0]],
    coverImage: '/images/java-thread.jpg',
    viewCount: 178,
    likeCount: 32,
    commentCount: 6,
    createdAt: '2024-01-12T16:45:00Z',
    updatedAt: '2024-01-12T16:45:00Z',
    isPublished: true
  },
  {
    id: 5,
    title: 'Spring Boot 微服务架构',
    content: '# Spring Boot 微服务架构\n\n微服务架构是现代应用开发的主流模式...',
    summary: '使用 Spring Boot 构建微服务架构，包括服务拆分、通信、部署等。',
    author: '张三',
    categoryId: 1,
    categoryName: 'Java',
    tags: [mockTags[0]],
    coverImage: '/images/microservice.jpg',
    viewCount: 312,
    likeCount: 67,
    commentCount: 12,
    createdAt: '2024-01-11T11:20:00Z',
    updatedAt: '2024-01-11T11:20:00Z',
    isPublished: true
  }
] 