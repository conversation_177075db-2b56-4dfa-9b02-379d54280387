/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  background:
    linear-gradient(45deg, rgba(220, 20, 60, 0.15), rgba(139, 0, 139, 0.15)),
    linear-gradient(rgba(0, 0, 0, 0.7), rgba(25, 25, 25, 0.9)),
    url('/images/background.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  animation: backgroundShift 20s ease-in-out infinite;
}

@keyframes backgroundShift {
  0%, 100% {
    background-position: center center;
  }
  50% {
    background-position: center top;
  }
}

/* 链接样式 */
a {
  text-decoration: none;
  color: inherit;
  transition: color 0.3s ease;
}

a:hover {
  color: #409eff;
}

/* 代码块样式 */
pre {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 16px;
  overflow-x: auto;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

code {
  background-color: #f6f8fa;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
}

/* 响应式设计 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 玻璃效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

/* 悬浮动画 */
.floating {
  animation: floating 3s ease-in-out infinite;
}

@keyframes floating {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* 渐入动画 */
.fade-in {
  animation: fadeIn 0.8s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 红色主题强调色 */
.accent-red {
  color: #dc143c !important;
  text-shadow: 0 0 5px rgba(220, 20, 60, 0.3);
}

/* 红色发光效果 */
.glow-red {
  box-shadow: 0 0 20px rgba(220, 20, 60, 0.3);
  border: 1px solid rgba(220, 20, 60, 0.2);
}

/* 脉冲动画 */
.pulse-red {
  animation: pulseRed 2s ease-in-out infinite;
}

@keyframes pulseRed {
  0%, 100% {
    box-shadow: 0 0 20px rgba(220, 20, 60, 0.3);
  }
  50% {
    box-shadow: 0 0 30px rgba(220, 20, 60, 0.6);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.5);
}

@media (max-width: 768px) {
  .container {
    padding: 0 15px;
  }
}
