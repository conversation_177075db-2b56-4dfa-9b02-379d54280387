<template>
  <div class="about-page">
    <div class="about-container">
      <!-- 个人介绍 -->
      <section class="profile-section">
        <div class="profile-header">
          <div class="avatar">
            <img src="/avatar.jpg" alt="头像" />
          </div>
          <div class="profile-info">
            <h1 class="name">博主姓名</h1>
            <p class="title">全栈开发工程师</p>
            <div class="social-links">
              <a href="https://github.com" target="_blank" rel="noopener noreferrer">
                <el-icon><Link /></el-icon>
                GitHub
              </a>
              <a href="mailto:<EMAIL>">
                <el-icon><Message /></el-icon>
                Email
              </a>
              <a href="https://weibo.com" target="_blank" rel="noopener noreferrer">
                <el-icon><Share /></el-icon>
                微博
              </a>
            </div>
          </div>
        </div>
      </section>

      <!-- 关于我 -->
      <section class="about-section">
        <h2 class="section-title">关于我</h2>
        <div class="about-content">
          <p>
            你好！我是一名热爱技术的全栈开发工程师，专注于前端和后端技术的学习与实践。
            这个博客是我分享技术心得、记录学习历程的地方。
          </p>
          <p>
            我相信技术能够改变世界，也希望通过我的分享能够帮助到更多的开发者。
            如果你对我的文章有任何疑问或建议，欢迎随时与我交流。
          </p>
        </div>
      </section>

      <!-- 技能栈 -->
      <section class="skills-section">
        <h2 class="section-title">技能栈</h2>
        <div class="skills-grid">
          <div class="skill-category">
            <h3>前端技术</h3>
            <div class="skill-tags">
              <el-tag>Vue.js</el-tag>
              <el-tag>React</el-tag>
              <el-tag>TypeScript</el-tag>
              <el-tag>JavaScript</el-tag>
              <el-tag>HTML/CSS</el-tag>
              <el-tag>Webpack</el-tag>
              <el-tag>Vite</el-tag>
            </div>
          </div>
          
          <div class="skill-category">
            <h3>后端技术</h3>
            <div class="skill-tags">
              <el-tag type="success">Node.js</el-tag>
              <el-tag type="success">Java</el-tag>
              <el-tag type="success">Spring Boot</el-tag>
              <el-tag type="success">Python</el-tag>
              <el-tag type="success">MySQL</el-tag>
              <el-tag type="success">Redis</el-tag>
              <el-tag type="success">MongoDB</el-tag>
            </div>
          </div>
          
          <div class="skill-category">
            <h3>工具与平台</h3>
            <div class="skill-tags">
              <el-tag type="warning">Git</el-tag>
              <el-tag type="warning">Docker</el-tag>
              <el-tag type="warning">Linux</el-tag>
              <el-tag type="warning">AWS</el-tag>
              <el-tag type="warning">Nginx</el-tag>
              <el-tag type="warning">Jenkins</el-tag>
            </div>
          </div>
        </div>
      </section>

      <!-- 博客统计 -->
      <section class="stats-section">
        <h2 class="section-title">博客统计</h2>
        <div class="stats-grid">
          <div class="stat-card">
            <div class="stat-number">{{ totalArticles }}</div>
            <div class="stat-label">文章总数</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalCategories }}</div>
            <div class="stat-label">分类数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalTags }}</div>
            <div class="stat-label">标签数量</div>
          </div>
          <div class="stat-card">
            <div class="stat-number">{{ totalViews }}</div>
            <div class="stat-label">总访问量</div>
          </div>
        </div>
      </section>

      <!-- 联系方式 -->
      <section class="contact-section">
        <h2 class="section-title">联系我</h2>
        <div class="contact-content">
          <p>如果你想与我交流技术问题，或者有任何建议和意见，欢迎通过以下方式联系我：</p>
          <div class="contact-methods">
            <div class="contact-item">
              <el-icon><Message /></el-icon>
              <span>邮箱: <EMAIL></span>
            </div>
            <div class="contact-item">
              <el-icon><Link /></el-icon>
              <span>GitHub: https://github.com/yourusername</span>
            </div>
            <div class="contact-item">
              <el-icon><ChatDotRound /></el-icon>
              <span>微信: your-wechat-id</span>
            </div>
          </div>
        </div>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Link, Message, Share, ChatDotRound } from '@element-plus/icons-vue'
import { useCategoryStore } from '@/stores/category'
import { useTagStore } from '@/stores/tag'

const categoryStore = useCategoryStore()
const tagStore = useTagStore()

// 计算属性
const totalArticles = computed(() => {
  return categoryStore.categories.reduce((total, category) => total + category.articleCount, 0)
})

const totalCategories = computed(() => categoryStore.categories.length)
const totalTags = computed(() => tagStore.tags.length)
const totalViews = computed(() => '10.5K') // 这里可以从API获取真实数据

// 生命周期
onMounted(() => {
  categoryStore.fetchCategories()
  tagStore.fetchTags()
})
</script>

<style scoped>
.about-page {
  max-width: 800px;
  margin: 0 auto;
}

.about-container {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-section {
  margin-bottom: 50px;
}

.profile-header {
  display: flex;
  gap: 30px;
  align-items: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  flex-shrink: 0;
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.name {
  margin: 0 0 10px;
  font-size: 28px;
  font-weight: bold;
  color: #333;
}

.title {
  margin: 0 0 20px;
  font-size: 16px;
  color: #666;
}

.social-links {
  display: flex;
  gap: 20px;
}

.social-links a {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #409eff;
  text-decoration: none;
  font-size: 14px;
  transition: color 0.3s ease;
}

.social-links a:hover {
  color: #66b1ff;
}

.section-title {
  margin: 0 0 25px;
  font-size: 24px;
  font-weight: bold;
  color: #333;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.about-section,
.skills-section,
.stats-section,
.contact-section {
  margin-bottom: 50px;
}

.about-content p {
  margin: 0 0 15px;
  line-height: 1.8;
  color: #333;
  font-size: 16px;
}

.skills-grid {
  display: grid;
  gap: 30px;
}

.skill-category h3 {
  margin: 0 0 15px;
  font-size: 18px;
  color: #333;
}

.skill-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 20px;
}

.stat-card {
  text-align: center;
  padding: 30px 20px;
  background-color: #f8f9fa;
  border-radius: 8px;
  transition: transform 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-number {
  font-size: 32px;
  font-weight: bold;
  color: #409eff;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 14px;
  color: #666;
}

.contact-content p {
  margin: 0 0 20px;
  line-height: 1.8;
  color: #333;
}

.contact-methods {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.contact-item {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 16px;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .about-container {
    padding: 20px;
  }
  
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .name {
    font-size: 24px;
  }
  
  .section-title {
    font-size: 20px;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .social-links {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .skill-tags {
    justify-content: center;
  }
}
</style>
