<template>
  <div class="category-page">
    <div class="page-header">
      <div v-if="category" class="category-info">
        <h1 class="category-title">{{ category.name }}</h1>
        <p class="category-description">{{ category.description }}</p>
        <div class="category-stats">
          <span class="article-count">共 {{ category.articleCount }} 篇文章</span>
        </div>
      </div>
      <div v-else class="category-loading">
        <el-skeleton :rows="3" animated />
      </div>
    </div>

    <div class="articles-section">
      <div v-loading="articleStore.loading" class="articles-list">
        <ArticleCard
          v-for="article in articles"
          :key="article.id"
          :article="article"
          @click="goToArticle(article.id)"
        />
      </div>

      <div v-if="articles.length === 0 && !articleStore.loading" class="no-articles">
        <el-empty description="该分类下暂无文章" />
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useArticleStore } from '@/stores/article'
import { useCategoryStore } from '@/stores/category'
import ArticleCard from '@/components/article/ArticleCard.vue'

const route = useRoute()
const router = useRouter()
const articleStore = useArticleStore()
const categoryStore = useCategoryStore()

// 响应式数据
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const categoryId = computed(() => Number(route.params.id))
const category = computed(() => categoryStore.currentCategory)
const articles = computed(() => articleStore.articles)
const pagination = computed(() => articleStore.pagination)

// 方法
const goToArticle = (id: number) => {
  router.push(`/article/${id}`)
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchArticles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchArticles()
}

const fetchArticles = () => {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value,
    categoryId: categoryId.value
  }
  articleStore.fetchArticles(params)
}

// 生命周期
onMounted(() => {
  if (categoryId.value) {
    categoryStore.fetchCategoryById(categoryId.value)
    fetchArticles()
  }
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    const id = Number(newId)
    categoryStore.fetchCategoryById(id)
    currentPage.value = 1
    fetchArticles()
  }
})
</script>

<style scoped>
.category-page {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.page-header {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.category-info {
  max-width: 600px;
  margin: 0 auto;
}

.category-title {
  margin: 0 0 15px;
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.category-description {
  margin: 0 0 20px;
  font-size: 16px;
  color: #666;
  line-height: 1.6;
}

.category-stats {
  font-size: 14px;
  color: #999;
}

.article-count {
  padding: 5px 15px;
  background-color: #f0f0f0;
  border-radius: 15px;
}

.articles-section {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.articles-list {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.no-articles {
  text-align: center;
  padding: 60px 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    padding: 20px;
  }
  
  .category-title {
    font-size: 24px;
  }
  
  .articles-section {
    padding: 20px;
  }
}
</style>
