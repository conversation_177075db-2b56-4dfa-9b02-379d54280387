<template>
  <div class="home">
    <!-- 欢迎横幅 -->
    <WelcomeBanner />

    <!-- 轮播图/特色文章 -->
    <div class="hero-section">
      <el-carousel height="300px" indicator-position="outside">
        <el-carousel-item
          v-for="article in featuredArticles"
          :key="article.id"
          @click="goToArticle(article.id)"
        >
          <div class="carousel-item">
            <div
              class="carousel-bg"
              :style="{ backgroundImage: `url(${article.coverImage || '/default-cover.jpg'})` }"
            >
              <div class="carousel-content">
                <h2 class="carousel-title">{{ article.title }}</h2>
                <p class="carousel-summary">{{ article.summary }}</p>
                <div class="carousel-meta">
                  <span>{{ formatDate(article.createdAt) }}</span>
                  <span>{{ article.categoryName }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 文章列表 -->
    <div class="articles-section">
      <div class="section-header">
        <h2>最新文章</h2>
        <div class="filter-tabs">
          <el-tabs v-model="activeTab" @tab-change="handleTabChange">
            <el-tab-pane label="全部" name="all" />
            <el-tab-pane
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :name="category.id.toString()"
            />
          </el-tabs>
        </div>
      </div>

      <div v-loading="articleStore.loading" class="articles-list">
        <ArticleCard
          v-for="article in articles"
          :key="article.id"
          :article="article"
          @click="goToArticle(article.id)"
        />
      </div>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useArticleStore } from '@/stores/article'
import { useCategoryStore } from '@/stores/category'
import ArticleCard from '@/components/article/ArticleCard.vue'
import WelcomeBanner from '@/components/WelcomeBanner.vue'
import dayjs from 'dayjs'

const router = useRouter()
const articleStore = useArticleStore()
const categoryStore = useCategoryStore()

// 响应式数据
const activeTab = ref('all')
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const articles = computed(() => articleStore.articles)
const categories = computed(() => categoryStore.categories)
const pagination = computed(() => articleStore.pagination)
const featuredArticles = computed(() => articles.value.slice(0, 3))

// 方法
const goToArticle = (id: number) => {
  router.push(`/article/${id}`)
}

const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日')
}

const handleTabChange = (tabName: string) => {
  currentPage.value = 1
  fetchArticles()
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  fetchArticles()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  fetchArticles()
}

const fetchArticles = () => {
  const params = {
    page: currentPage.value,
    pageSize: pageSize.value,
    categoryId: activeTab.value === 'all' ? undefined : Number(activeTab.value)
  }
  articleStore.fetchArticles(params)
}

// 生命周期
onMounted(() => {
  categoryStore.fetchCategories()
  fetchArticles()
})

// 监听路由变化
watch(() => activeTab.value, () => {
  fetchArticles()
})
</script>

<style scoped>
/* 主页样式 */
.home {
  min-height: 100vh;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

/* 轮播图样式 */
.hero-section {
  margin-bottom: 30px;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.carousel-item {
  height: 300px;
  cursor: pointer;
  position: relative;
}

.carousel-bg {
  width: 100%;
  height: 100%;
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  position: relative;
}

.carousel-bg::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(to bottom, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.7));
}

.carousel-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 30px;
  color: white;
  z-index: 2;
}

.carousel-title {
  font-size: 24px;
  font-weight: bold;
  margin: 0 0 10px;
  line-height: 1.3;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.carousel-summary {
  font-size: 16px;
  margin: 0 0 15px;
  line-height: 1.5;
  opacity: 0.9;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.carousel-meta {
  display: flex;
  gap: 20px;
  font-size: 14px;
  opacity: 0.8;
}

/* 文章列表样式 */
.articles-section {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 30px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
}

.section-header h2 {
  margin: 0;
  font-size: 24px;
  color: #333;
  font-weight: 600;
}

.filter-tabs {
  flex: 1;
  max-width: 600px;
  margin-left: 30px;
}

.articles-list {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    align-items: stretch;
    gap: 20px;
  }
  
  .filter-tabs {
    margin-left: 0;
    max-width: none;
  }
  
  .carousel-content {
    bottom: 20px;
    left: 20px;
    right: 20px;
  }
  
  .carousel-title {
    font-size: 20px;
  }
  
  .carousel-summary {
    font-size: 14px;
  }
  
  .articles-section {
    padding: 20px;
  }
}
</style>
