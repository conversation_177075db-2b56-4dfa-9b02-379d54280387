<template>
  <div class="article-detail">
    <div v-loading="articleStore.loading" class="article-container">
      <div v-if="article" class="article-content">
        <!-- 文章头部 -->
        <header class="article-header">
          <h1 class="article-title">{{ article.title }}</h1>
          <div class="article-meta">
            <div class="meta-info">
              <span class="author">
                <el-icon><User /></el-icon>
                {{ article.author }}
              </span>
              <span class="date">
                <el-icon><Calendar /></el-icon>
                {{ formatDate(article.createdAt) }}
              </span>
              <span class="category">
                <el-icon><Folder /></el-icon>
                <router-link :to="`/category/${article.categoryId}`">
                  {{ article.categoryName }}
                </router-link>
              </span>
            </div>
            <div class="meta-stats">
              <span class="view-count">
                <el-icon><View /></el-icon>
                {{ article.viewCount }}
              </span>
              <span class="like-count">
                <el-icon><Star /></el-icon>
                {{ article.likeCount }}
              </span>
              <span class="comment-count">
                <el-icon><ChatDotRound /></el-icon>
                {{ article.commentCount }}
              </span>
            </div>
          </div>
          
          <!-- 标签 -->
          <div class="article-tags">
            <el-tag
              v-for="tag in article.tags"
              :key="tag.id"
              :color="tag.color"
              @click="goToTag(tag.id)"
            >
              {{ tag.name }}
            </el-tag>
          </div>
        </header>

        <!-- 封面图片 -->
        <div v-if="article.coverImage" class="cover-image">
          <img :src="article.coverImage" :alt="article.title" />
        </div>

        <!-- 文章正文 -->
        <div class="article-body">
          <div v-html="renderedContent" class="markdown-content"></div>
        </div>

        <!-- 文章操作 -->
        <div class="article-actions">
          <el-button type="primary" @click="likeArticle" :loading="liking">
            <el-icon><Star /></el-icon>
            点赞 ({{ article.likeCount }})
          </el-button>
          <el-button @click="shareArticle">
            <el-icon><Share /></el-icon>
            分享
          </el-button>
        </div>

        <!-- 相关文章 -->
        <div v-if="relatedArticles.length > 0" class="related-articles">
          <h3>相关文章</h3>
          <div class="related-list">
            <div
              v-for="relatedArticle in relatedArticles"
              :key="relatedArticle.id"
              class="related-item"
              @click="goToArticle(relatedArticle.id)"
            >
              <h4>{{ relatedArticle.title }}</h4>
              <p>{{ relatedArticle.summary }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 文章不存在 -->
      <div v-else-if="!articleStore.loading" class="article-not-found">
        <el-empty description="文章不存在或已被删除" />
      </div>
    </div>

    <!-- 评论区域 -->
    <CommentSection v-if="article" :article-id="article.id" />
  </div>
</template>

<script setup lang="ts">
import { User, Calendar, Folder, View, Star, ChatDotRound, Share } from '@element-plus/icons-vue'
import { useArticleStore } from '@/stores/article'
import CommentSection from '@/components/comment/CommentSection.vue'
import { marked } from 'marked'
import hljs from 'highlight.js'
import 'highlight.js/styles/github.css'
import dayjs from 'dayjs'

const route = useRoute()
const router = useRouter()
const articleStore = useArticleStore()

// 响应式数据
const liking = ref(false)
const relatedArticles = ref([])

// 计算属性
const article = computed(() => articleStore.currentArticle)
const renderedContent = computed(() => {
  if (!article.value?.content) return ''
  
  // 配置marked
  marked.setOptions({
    highlight: function(code, lang) {
      if (lang && hljs.getLanguage(lang)) {
        try {
          return hljs.highlight(code, { language: lang }).value
        } catch (err) {}
      }
      return hljs.highlightAuto(code).value
    }
  })
  
  return marked(article.value.content)
})

// 方法
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY年MM月DD日 HH:mm')
}

const goToTag = (id: number) => {
  router.push(`/tag/${id}`)
}

const goToArticle = (id: number) => {
  router.push(`/article/${id}`)
}

const likeArticle = async () => {
  if (!article.value) return
  
  liking.value = true
  try {
    await articleStore.likeArticle(article.value.id)
  } finally {
    liking.value = false
  }
}

const shareArticle = () => {
  if (navigator.share) {
    navigator.share({
      title: article.value?.title,
      url: window.location.href
    })
  } else {
    // 复制链接到剪贴板
    navigator.clipboard.writeText(window.location.href)
    ElMessage.success('链接已复制到剪贴板')
  }
}

// 生命周期
onMounted(() => {
  const articleId = Number(route.params.id)
  if (articleId) {
    articleStore.fetchArticleById(articleId)
  }
})

// 监听路由变化
watch(() => route.params.id, (newId) => {
  if (newId) {
    articleStore.fetchArticleById(Number(newId))
  }
})
</script>

<style scoped>
.article-detail {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.article-container {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.article-header {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.article-title {
  margin: 0 0 20px;
  font-size: 32px;
  font-weight: bold;
  color: #333;
  line-height: 1.3;
}

.article-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  font-size: 14px;
  color: #666;
}

.meta-info,
.meta-stats {
  display: flex;
  gap: 20px;
}

.meta-info span,
.meta-stats span {
  display: flex;
  align-items: center;
  gap: 5px;
}

.category a {
  color: #409eff;
  text-decoration: none;
}

.category a:hover {
  text-decoration: underline;
}

.article-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.article-tags .el-tag {
  cursor: pointer;
}

.cover-image {
  margin-bottom: 30px;
  text-align: center;
}

.cover-image img {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.article-body {
  margin-bottom: 40px;
}

.markdown-content {
  line-height: 1.8;
  color: #333;
  font-size: 16px;
}

.markdown-content :deep(h1),
.markdown-content :deep(h2),
.markdown-content :deep(h3),
.markdown-content :deep(h4),
.markdown-content :deep(h5),
.markdown-content :deep(h6) {
  margin: 30px 0 15px;
  color: #2c3e50;
}

.markdown-content :deep(p) {
  margin: 15px 0;
}

.markdown-content :deep(img) {
  max-width: 100%;
  height: auto;
  border-radius: 4px;
  margin: 15px 0;
}

.markdown-content :deep(blockquote) {
  border-left: 4px solid #409eff;
  padding-left: 20px;
  margin: 20px 0;
  color: #666;
  background-color: #f8f9fa;
  padding: 15px 20px;
  border-radius: 4px;
}

.markdown-content :deep(code) {
  background-color: #f6f8fa;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

.markdown-content :deep(pre) {
  background-color: #f6f8fa;
  border-radius: 6px;
  padding: 20px;
  overflow-x: auto;
  margin: 20px 0;
}

.markdown-content :deep(pre code) {
  background: none;
  padding: 0;
}

.article-actions {
  display: flex;
  gap: 15px;
  padding: 20px 0;
  border-top: 1px solid #f0f0f0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 30px;
}

.related-articles h3 {
  margin: 0 0 20px;
  font-size: 20px;
  color: #333;
}

.related-list {
  display: grid;
  gap: 15px;
}

.related-item {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.related-item:hover {
  background-color: #e9ecef;
}

.related-item h4 {
  margin: 0 0 8px;
  font-size: 16px;
  color: #333;
}

.related-item p {
  margin: 0;
  color: #666;
  font-size: 14px;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.article-not-found {
  text-align: center;
  padding: 60px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .article-container {
    padding: 20px;
  }
  
  .article-title {
    font-size: 24px;
  }
  
  .article-meta {
    flex-direction: column;
    align-items: stretch;
    gap: 10px;
  }
  
  .meta-info,
  .meta-stats {
    justify-content: space-between;
  }
  
  .markdown-content {
    font-size: 15px;
  }
}
</style>
