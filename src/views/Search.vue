<template>
  <div class="search-page">
    <div class="search-header">
      <h1 class="page-title">搜索结果</h1>
      <div class="search-form">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索文章..."
          size="large"
          @keyup.enter="handleSearch"
          class="search-input"
        >
          <template #append>
            <el-button @click="handleSearch" :loading="searching">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
      
      <div v-if="searchKeyword" class="search-info">
        <p>搜索关键词: <strong>"{{ searchKeyword }}"</strong></p>
        <p v-if="!articleStore.loading">找到 {{ pagination.total }} 篇相关文章</p>
      </div>
    </div>

    <div class="search-filters">
      <div class="filter-item">
        <label>分类筛选:</label>
        <el-select
          v-model="selectedCategory"
          placeholder="选择分类"
          clearable
          @change="handleCategoryChange"
        >
          <el-option
            v-for="category in categories"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </div>
      
      <div class="filter-item">
        <label>标签筛选:</label>
        <el-select
          v-model="selectedTag"
          placeholder="选择标签"
          clearable
          @change="handleTagChange"
        >
          <el-option
            v-for="tag in tags"
            :key="tag.id"
            :label="tag.name"
            :value="tag.id"
          />
        </el-select>
      </div>
    </div>

    <div class="search-results">
      <div v-loading="articleStore.loading" class="articles-list">
        <ArticleCard
          v-for="article in articles"
          :key="article.id"
          :article="article"
          @click="goToArticle(article.id)"
        />
      </div>

      <div v-if="articles.length === 0 && !articleStore.loading && searchKeyword" class="no-results">
        <el-empty description="没有找到相关文章，请尝试其他关键词" />
      </div>

      <div v-if="!searchKeyword && !articleStore.loading" class="search-tips">
        <el-empty description="请输入关键词开始搜索" />
      </div>

      <!-- 分页 -->
      <div v-if="pagination.total > 0" class="pagination-wrapper">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Search } from '@element-plus/icons-vue'
import { useArticleStore } from '@/stores/article'
import { useCategoryStore } from '@/stores/category'
import { useTagStore } from '@/stores/tag'
import ArticleCard from '@/components/article/ArticleCard.vue'

const route = useRoute()
const router = useRouter()
const articleStore = useArticleStore()
const categoryStore = useCategoryStore()
const tagStore = useTagStore()

// 响应式数据
const searchKeyword = ref('')
const selectedCategory = ref<number | undefined>()
const selectedTag = ref<number | undefined>()
const currentPage = ref(1)
const pageSize = ref(10)
const searching = ref(false)

// 计算属性
const articles = computed(() => articleStore.articles)
const categories = computed(() => categoryStore.categories)
const tags = computed(() => tagStore.tags)
const pagination = computed(() => articleStore.pagination)

// 方法
const goToArticle = (id: number) => {
  router.push(`/article/${id}`)
}

const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  searching.value = true
  currentPage.value = 1
  
  try {
    await articleStore.searchArticles({
      keyword: searchKeyword.value.trim(),
      categoryId: selectedCategory.value,
      tagId: selectedTag.value,
      page: currentPage.value,
      pageSize: pageSize.value
    })
    
    // 更新URL
    router.push({
      path: '/search',
      query: {
        keyword: searchKeyword.value.trim(),
        ...(selectedCategory.value && { categoryId: selectedCategory.value }),
        ...(selectedTag.value && { tagId: selectedTag.value })
      }
    })
  } finally {
    searching.value = false
  }
}

const handleCategoryChange = () => {
  if (searchKeyword.value.trim()) {
    handleSearch()
  }
}

const handleTagChange = () => {
  if (searchKeyword.value.trim()) {
    handleSearch()
  }
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  if (searchKeyword.value.trim()) {
    handleSearch()
  }
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  if (searchKeyword.value.trim()) {
    handleSearch()
  }
}

// 生命周期
onMounted(() => {
  // 从URL获取搜索参数
  const keyword = route.query.keyword as string
  const categoryId = route.query.categoryId as string
  const tagId = route.query.tagId as string
  
  if (keyword) {
    searchKeyword.value = keyword
    selectedCategory.value = categoryId ? Number(categoryId) : undefined
    selectedTag.value = tagId ? Number(tagId) : undefined
    handleSearch()
  }
  
  // 加载分类和标签数据
  categoryStore.fetchCategories()
  tagStore.fetchTags()
})

// 监听路由变化
watch(() => route.query, (newQuery) => {
  const keyword = newQuery.keyword as string
  if (keyword && keyword !== searchKeyword.value) {
    searchKeyword.value = keyword
    handleSearch()
  }
})
</script>

<style scoped>
.search-page {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.search-header {
  background: white;
  border-radius: 8px;
  padding: 40px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.page-title {
  margin: 0 0 30px;
  font-size: 32px;
  font-weight: bold;
  color: #333;
}

.search-form {
  max-width: 600px;
  margin: 0 auto 20px;
}

.search-input {
  width: 100%;
}

.search-info {
  font-size: 14px;
  color: #666;
}

.search-info p {
  margin: 5px 0;
}

.search-filters {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  gap: 30px;
  align-items: center;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.filter-item label {
  font-size: 14px;
  color: #333;
  white-space: nowrap;
}

.search-results {
  background: white;
  border-radius: 8px;
  padding: 30px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.articles-list {
  display: grid;
  gap: 20px;
  margin-bottom: 30px;
}

.no-results,
.search-tips {
  text-align: center;
  padding: 60px 0;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  padding-top: 20px;
  border-top: 1px solid #f0f0f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-header {
    padding: 20px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .search-filters {
    flex-direction: column;
    align-items: stretch;
    gap: 15px;
  }
  
  .filter-item {
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
  }
  
  .search-results {
    padding: 20px;
  }
}
</style>
