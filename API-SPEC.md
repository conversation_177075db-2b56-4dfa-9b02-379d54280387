# API 接口文档

## 概述

本文档描述了Vue个人博客项目的API接口规范。所有接口都使用RESTful风格，返回JSON格式数据。

## 基础信息

- **基础URL**: `/api`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: 暂无（开发阶段）

## 通用响应格式

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {}
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 接口列表

### 1. 分类相关接口

#### 1.1 获取所有分类

- **URL**: `GET /categories`
- **描述**: 获取所有文章分类
- **参数**: 无
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取分类成功",
  "data": [
    {
      "id": 1,
      "name": "Java",
      "description": "Java编程技术相关文章",
      "articleCount": 3,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

#### 1.2 获取分类详情

- **URL**: `GET /categories/{id}`
- **描述**: 根据ID获取分类详情
- **参数**: 
  - `id` (path): 分类ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取分类详情成功",
  "data": {
    "id": 1,
    "name": "Java",
    "description": "Java编程技术相关文章",
    "articleCount": 3,
    "createdAt": "2024-01-15T10:00:00Z"
  }
}
```

### 2. 文章相关接口

#### 2.1 获取文章列表

- **URL**: `GET /articles`
- **描述**: 获取文章列表，支持分页和筛选
- **参数**:
  - `page` (query): 页码，默认1
  - `pageSize` (query): 每页数量，默认10
  - `categoryId` (query): 分类ID，可选
  - `tagId` (query): 标签ID，可选
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取文章列表成功",
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "Spring Boot 入门指南",
        "summary": "本文介绍 Spring Boot 的基本概念和使用方法",
        "author": "张三",
        "categoryId": 1,
        "categoryName": "Java",
        "tags": [
          {
            "id": 1,
            "name": "Spring Boot",
            "color": "#67C23A"
          }
        ],
        "coverImage": "/images/spring-boot.jpg",
        "viewCount": 156,
        "likeCount": 23,
        "commentCount": 5,
        "createdAt": "2024-01-15T10:00:00Z",
        "updatedAt": "2024-01-15T10:00:00Z",
        "isPublished": true
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

#### 2.2 获取文章详情

- **URL**: `GET /articles/{id}`
- **描述**: 根据ID获取文章详情
- **参数**:
  - `id` (path): 文章ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取文章详情成功",
  "data": {
    "id": 1,
    "title": "Spring Boot 入门指南",
    "content": "# Spring Boot 入门指南\n\nSpring Boot 是一个基于 Spring 框架的快速开发框架...",
    "summary": "本文介绍 Spring Boot 的基本概念和使用方法",
    "author": "张三",
    "categoryId": 1,
    "categoryName": "Java",
    "tags": [
      {
        "id": 1,
        "name": "Spring Boot",
        "color": "#67C23A"
      }
    ],
    "coverImage": "/images/spring-boot.jpg",
    "viewCount": 156,
    "likeCount": 23,
    "commentCount": 5,
    "createdAt": "2024-01-15T10:00:00Z",
    "updatedAt": "2024-01-15T10:00:00Z",
    "isPublished": true
  }
}
```

#### 2.3 搜索文章

- **URL**: `GET /articles/search`
- **描述**: 搜索文章
- **参数**:
  - `keyword` (query): 搜索关键词
  - `page` (query): 页码，默认1
  - `pageSize` (query): 每页数量，默认10
  - `categoryId` (query): 分类ID，可选
  - `tagId` (query): 标签ID，可选
- **响应格式**: 同获取文章列表

#### 2.4 获取热门文章

- **URL**: `GET /articles/hot`
- **描述**: 获取热门文章
- **参数**:
  - `limit` (query): 返回数量，默认5
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取热门文章成功",
  "data": [
    {
      "id": 1,
      "title": "Spring Boot 入门指南",
      "summary": "本文介绍 Spring Boot 的基本概念和使用方法",
      "viewCount": 156,
      "likeCount": 23,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

#### 2.5 获取最新文章

- **URL**: `GET /articles/latest`
- **描述**: 获取最新文章
- **参数**:
  - `limit` (query): 返回数量，默认5
- **响应格式**: 同获取热门文章

#### 2.6 增加文章浏览量

- **URL**: `POST /articles/{id}/view`
- **描述**: 增加文章浏览量
- **参数**:
  - `id` (path): 文章ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "增加浏览量成功",
  "data": null
}
```

#### 2.7 点赞文章

- **URL**: `POST /articles/{id}/like`
- **描述**: 点赞文章
- **参数**:
  - `id` (path): 文章ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "点赞成功",
  "data": null
}
```

### 3. 标签相关接口

#### 3.1 获取所有标签

- **URL**: `GET /tags`
- **描述**: 获取所有标签
- **参数**: 无
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取标签列表成功",
  "data": [
    {
      "id": 1,
      "name": "Spring Boot",
      "color": "#67C23A",
      "articleCount": 2,
      "createdAt": "2024-01-15T10:00:00Z"
    }
  ]
}
```

#### 3.2 获取热门标签

- **URL**: `GET /tags/hot`
- **描述**: 获取热门标签
- **参数**:
  - `limit` (query): 返回数量，默认10
- **响应格式**: 同获取所有标签

#### 3.3 获取标签详情

- **URL**: `GET /tags/{id}`
- **描述**: 根据ID获取标签详情
- **参数**:
  - `id` (path): 标签ID
- **响应格式**: 同获取所有标签

### 4. 评论相关接口

#### 4.1 获取文章评论

- **URL**: `GET /comments/article/{articleId}`
- **描述**: 获取指定文章的评论
- **参数**:
  - `articleId` (path): 文章ID
- **响应示例**:
```json
{
  "code": 200,
  "message": "获取评论成功",
  "data": [
    {
      "id": 1,
      "articleId": 1,
      "content": "这篇文章写得很好！",
      "author": "小明",
      "email": "<EMAIL>",
      "createdAt": "2024-01-15T11:30:00Z",
      "isApproved": true
    }
  ]
}
```

#### 4.2 添加评论

- **URL**: `POST /comments`
- **描述**: 添加评论
- **参数**:
  - `articleId` (body): 文章ID
  - `content` (body): 评论内容
  - `author` (body): 作者姓名
  - `email` (body): 邮箱
  - `website` (body): 网站，可选
  - `parentId` (body): 父评论ID，可选
- **响应示例**:
```json
{
  "code": 200,
  "message": "添加评论成功",
  "data": {
    "id": 2,
    "articleId": 1,
    "content": "感谢分享！",
    "author": "小红",
    "email": "<EMAIL>",
    "createdAt": "2024-01-15T12:00:00Z",
    "isApproved": true
  }
}
```

#### 4.3 获取最新评论

- **URL**: `GET /comments/latest`
- **描述**: 获取最新评论
- **参数**:
  - `limit` (query): 返回数量，默认5
- **响应格式**: 同获取文章评论

## 数据类型定义

### Category (分类)
```typescript
interface Category {
  id: number;
  name: string;
  description: string;
  articleCount: number;
  createdAt: string;
}
```

### Article (文章)
```typescript
interface Article {
  id: number;
  title: string;
  content: string;
  summary: string;
  author: string;
  categoryId: number;
  categoryName: string;
  tags: Tag[];
  coverImage?: string;
  viewCount: number;
  likeCount: number;
  commentCount: number;
  createdAt: string;
  updatedAt: string;
  isPublished: boolean;
}
```

### Tag (标签)
```typescript
interface Tag {
  id: number;
  name: string;
  color: string;
  articleCount: number;
  createdAt: string;
}
```

### Comment (评论)
```typescript
interface Comment {
  id: number;
  articleId: number;
  content: string;
  author: string;
  email: string;
  website?: string;
  parentId?: number;
  children?: Comment[];
  createdAt: string;
  isApproved: boolean;
}
```

### ApiResponse (API响应)
```typescript
interface ApiResponse<T = any> {
  code: number;
  message: string;
  data: T;
}
```

## 注意事项

1. 所有时间字段使用ISO 8601格式
2. 图片路径为相对路径，需要配合静态资源服务器
3. 当前版本使用模拟数据，实际部署时需要连接真实数据库
4. 评论功能需要审核机制，`isApproved`字段控制是否显示
5. 分页参数从1开始计数

## 更新日志

- **v1.0.0** (2024-01-15): 初始版本，包含基础的CRUD接口
- **v1.1.0** (2024-01-16): 添加搜索和筛选功能
- **v1.2.0** (2024-01-17): 添加评论系统
