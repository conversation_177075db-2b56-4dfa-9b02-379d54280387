# 文章接口

## 数据模型

### Article

```typescript
interface Article {
  id: number
  title: string
  content: string
  summary: string
  author: string
  categoryId: number
  categoryName: string
  tags: Tag[]
  coverImage?: string
  viewCount: number
  likeCount: number
  commentCount: number
  createdAt: string
  updatedAt: string
  isPublished: boolean
}
```

## 接口列表

### 获取文章列表

**GET** `/articles`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 是 | 页码 |
| pageSize | number | 是 | 每页数量 |
| categoryId | number | 否 | 分类ID |
| tagId | number | 否 | 标签ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "articles": [
      {
        "id": 1,
        "title": "Vue3 入门指南",
        "content": "...",
        "summary": "Vue3 的基础知识介绍",
        "author": "张三",
        "categoryId": 1,
        "categoryName": "前端技术",
        "tags": [
          {
            "id": 1,
            "name": "Vue",
            "color": "#42b883"
          }
        ],
        "coverImage": "/images/vue3.jpg",
        "viewCount": 1000,
        "likeCount": 50,
        "commentCount": 10,
        "createdAt": "2024-01-01T00:00:00Z",
        "updatedAt": "2024-01-01T00:00:00Z",
        "isPublished": true
      }
    ],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

### 获取文章详情

**GET** `/articles/{id}`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | number | 是 | 文章ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "title": "Vue3 入门指南",
    "content": "# Vue3 入门指南\n\n这是一篇关于Vue3的文章...",
    "summary": "Vue3 的基础知识介绍",
    "author": "张三",
    "categoryId": 1,
    "categoryName": "前端技术",
    "tags": [
      {
        "id": 1,
        "name": "Vue",
        "color": "#42b883"
      }
    ],
    "coverImage": "/images/vue3.jpg",
    "viewCount": 1000,
    "likeCount": 50,
    "commentCount": 10,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:00:00Z",
    "isPublished": true
  }
}
```

### 搜索文章

**GET** `/articles/search`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| keyword | string | 是 | 搜索关键词 |
| page | number | 是 | 页码 |
| pageSize | number | 是 | 每页数量 |
| categoryId | number | 否 | 分类ID |
| tagId | number | 否 | 标签ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "articles": [...],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 获取热门文章

**GET** `/articles/hot`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回数量，默认5 |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "Vue3 入门指南",
      "summary": "Vue3 的基础知识介绍",
      "viewCount": 1000,
      "likeCount": 50,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取最新文章

**GET** `/articles/latest`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回数量，默认5 |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "title": "Vue3 入门指南",
      "summary": "Vue3 的基础知识介绍",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 增加文章浏览量

**POST** `/articles/{id}/view`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | number | 是 | 文章ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```

### 点赞文章

**POST** `/articles/{id}/like`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | number | 是 | 文章ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": null
}
```
