# API 接口文档

本文档描述了个人博客前端项目所需的后端 API 接口规范。

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **Content-Type**: `application/json`
- **字符编码**: UTF-8

## 通用响应格式

所有 API 接口都遵循统一的响应格式：

```json
{
  "code": 200,
  "message": "success",
  "data": {}
}
```

### 响应字段说明

| 字段 | 类型 | 说明 |
|------|------|------|
| code | number | 状态码，200表示成功 |
| message | string | 响应消息 |
| data | any | 响应数据 |

### 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 分页参数

对于需要分页的接口，使用以下参数：

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| page | number | 是 | 页码，从1开始 |
| pageSize | number | 是 | 每页数量 |

## 分页响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "items": [],
    "pagination": {
      "page": 1,
      "pageSize": 10,
      "total": 100,
      "totalPages": 10
    }
  }
}
```

## 错误处理

当请求失败时，响应格式如下：

```json
{
  "code": 400,
  "message": "参数错误",
  "data": null
}
```

## 接口列表

- [文章接口](./article.md)
- [分类接口](./category.md)
- [标签接口](./tag.md)
- [评论接口](./comment.md)
