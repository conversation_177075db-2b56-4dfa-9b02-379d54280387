# 标签接口

## 数据模型

### Tag

```typescript
interface Tag {
  id: number
  name: string
  color: string
  articleCount: number
  createdAt: string
}
```

## 接口列表

### 获取所有标签

**GET** `/tags`

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "Vue",
      "color": "#42b883",
      "articleCount": 15,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "React",
      "color": "#61dafb",
      "articleCount": 10,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取热门标签

**GET** `/tags/hot`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回数量，默认10 |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "Vue",
      "color": "#42b883",
      "articleCount": 15,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "JavaScript",
      "color": "#f7df1e",
      "articleCount": 12,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取标签详情

**GET** `/tags/{id}`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | number | 是 | 标签ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "Vue",
    "color": "#42b883",
    "articleCount": 15,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```
