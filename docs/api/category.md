# 分类接口

## 数据模型

### Category

```typescript
interface Category {
  id: number
  name: string
  description: string
  articleCount: number
  createdAt: string
}
```

## 接口列表

### 获取所有分类

**GET** `/categories`

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "name": "前端技术",
      "description": "前端开发相关技术文章",
      "articleCount": 25,
      "createdAt": "2024-01-01T00:00:00Z"
    },
    {
      "id": 2,
      "name": "后端技术",
      "description": "后端开发相关技术文章",
      "articleCount": 18,
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ]
}
```

### 获取分类详情

**GET** `/categories/{id}`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| id | number | 是 | 分类ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "id": 1,
    "name": "前端技术",
    "description": "前端开发相关技术文章，包括Vue、React、Angular等框架的使用心得和最佳实践。",
    "articleCount": 25,
    "createdAt": "2024-01-01T00:00:00Z"
  }
}
```
