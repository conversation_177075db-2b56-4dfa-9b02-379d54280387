# 评论接口

## 数据模型

### Comment

```typescript
interface Comment {
  id: number
  articleId: number
  content: string
  author: string
  email: string
  website?: string
  parentId?: number
  children?: Comment[]
  createdAt: string
  isApproved: boolean
}
```

## 接口列表

### 获取文章评论

**GET** `/comments/article/{articleId}`

#### 路径参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| articleId | number | 是 | 文章ID |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "articleId": 1,
      "content": "这篇文章写得很好！",
      "author": "张三",
      "email": "<EMAIL>",
      "website": "https://zhangsan.com",
      "parentId": null,
      "children": [
        {
          "id": 2,
          "articleId": 1,
          "content": "谢谢你的评论！",
          "author": "博主",
          "email": "<EMAIL>",
          "parentId": 1,
          "children": [],
          "createdAt": "2024-01-01T01:00:00Z",
          "isApproved": true
        }
      ],
      "createdAt": "2024-01-01T00:00:00Z",
      "isApproved": true
    }
  ]
}
```

### 添加评论

**POST** `/comments`

#### 请求体

```json
{
  "articleId": 1,
  "content": "这是一条评论",
  "author": "张三",
  "email": "<EMAIL>",
  "website": "https://zhangsan.com",
  "parentId": null
}
```

#### 请求参数说明

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| articleId | number | 是 | 文章ID |
| content | string | 是 | 评论内容 |
| author | string | 是 | 评论者姓名 |
| email | string | 是 | 评论者邮箱 |
| website | string | 否 | 评论者网站 |
| parentId | number | 否 | 父评论ID（回复评论时使用） |

#### 响应示例

```json
{
  "code": 200,
  "message": "评论发表成功",
  "data": {
    "id": 3,
    "articleId": 1,
    "content": "这是一条评论",
    "author": "张三",
    "email": "<EMAIL>",
    "website": "https://zhangsan.com",
    "parentId": null,
    "children": [],
    "createdAt": "2024-01-01T02:00:00Z",
    "isApproved": false
  }
}
```

### 获取最新评论

**GET** `/comments/latest`

#### 请求参数

| 参数 | 类型 | 必填 | 说明 |
|------|------|------|------|
| limit | number | 否 | 返回数量，默认5 |

#### 响应示例

```json
{
  "code": 200,
  "message": "success",
  "data": [
    {
      "id": 1,
      "articleId": 1,
      "content": "这篇文章写得很好！",
      "author": "张三",
      "email": "<EMAIL>",
      "createdAt": "2024-01-01T00:00:00Z",
      "isApproved": true
    }
  ]
}
```

## 注意事项

1. 评论需要审核，新发表的评论 `isApproved` 字段为 `false`
2. 只有审核通过的评论才会在前端显示
3. 邮箱地址不会在前端显示，仅用于后台管理
4. 评论支持嵌套回复，最多支持3层嵌套
