import { defineConfig } from 'vitepress'

export default defineConfig({
  title: 'Vue3 个人博客',
  description: '基于Vue3 + TypeScript的个人博客前端项目文档',
  
  themeConfig: {
    nav: [
      { text: '首页', link: '/' },
      { text: '组件文档', link: '/components/' },
      { text: 'API文档', link: '/api/' },
      { text: '部署指南', link: '/deployment/' }
    ],

    sidebar: {
      '/components/': [
        {
          text: '布局组件',
          items: [
            { text: 'HeaderComponent', link: '/components/layout/header' },
            { text: 'SidebarComponent', link: '/components/layout/sidebar' },
            { text: 'FooterComponent', link: '/components/layout/footer' }
          ]
        },
        {
          text: '文章组件',
          items: [
            { text: 'ArticleCard', link: '/components/article/article-card' },
            { text: 'ArticleDetail', link: '/components/article/article-detail' }
          ]
        },
        {
          text: '评论组件',
          items: [
            { text: 'CommentSection', link: '/components/comment/comment-section' },
            { text: 'CommentItem', link: '/components/comment/comment-item' }
          ]
        }
      ],
      '/api/': [
        {
          text: 'API接口',
          items: [
            { text: '文章接口', link: '/api/article' },
            { text: '分类接口', link: '/api/category' },
            { text: '标签接口', link: '/api/tag' },
            { text: '评论接口', link: '/api/comment' }
          ]
        }
      ]
    },

    socialLinks: [
      { icon: 'github', link: 'https://github.com' }
    ],

    footer: {
      message: 'Released under the MIT License.',
      copyright: 'Copyright © 2024 Vue3 Personal Blog'
    }
  }
})
