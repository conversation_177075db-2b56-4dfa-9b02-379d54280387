# 部署指南

本指南将帮助你将 Vue3 个人博客项目部署到生产环境。

## 构建项目

### 1. 安装依赖

```bash
npm install
```

### 2. 构建生产版本

```bash
npm run build
```

构建完成后，会在 `dist` 目录下生成静态文件。

### 3. 预览构建结果

```bash
npm run preview
```

## 部署方式

### 1. Nginx 部署

#### 安装 Nginx

```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nginx

# CentOS/RHEL
sudo yum install nginx
```

#### 配置 Nginx

创建配置文件 `/etc/nginx/sites-available/blog`:

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/blog/dist;
    index index.html;

    # 处理 Vue Router 的 history 模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API 代理
    location /api/ {
        proxy_pass http://localhost:8080/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Gzip 压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

#### 启用站点

```bash
sudo ln -s /etc/nginx/sites-available/blog /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 2. Apache 部署

#### 安装 Apache

```bash
# Ubuntu/Debian
sudo apt install apache2

# CentOS/RHEL
sudo yum install httpd
```

#### 配置 Apache

创建虚拟主机配置文件：

```apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot /var/www/blog/dist
    
    # 处理 Vue Router 的 history 模式
    <Directory "/var/www/blog/dist">
        RewriteEngine On
        RewriteBase /
        RewriteRule ^index\.html$ - [L]
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule . /index.html [L]
    </Directory>

    # API 代理
    ProxyPass /api/ http://localhost:8080/
    ProxyPassReverse /api/ http://localhost:8080/
</VirtualHost>
```

### 3. Docker 部署

#### 创建 Dockerfile

```dockerfile
# 构建阶段
FROM node:18-alpine as build-stage
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

# 生产阶段
FROM nginx:alpine as production-stage
COPY --from=build-stage /app/dist /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### 创建 nginx.conf

```nginx
events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    server {
        listen 80;
        server_name localhost;
        root /usr/share/nginx/html;
        index index.html;

        location / {
            try_files $uri $uri/ /index.html;
        }

        location /api/ {
            proxy_pass http://backend:8080/;
        }
    }
}
```

#### 构建和运行

```bash
# 构建镜像
docker build -t vue3-blog .

# 运行容器
docker run -d -p 80:80 vue3-blog
```

### 4. Vercel 部署

#### 安装 Vercel CLI

```bash
npm i -g vercel
```

#### 部署

```bash
vercel --prod
```

#### 配置文件 vercel.json

```json
{
  "rewrites": [
    {
      "source": "/api/(.*)",
      "destination": "https://your-api-domain.com/$1"
    },
    {
      "source": "/(.*)",
      "destination": "/index.html"
    }
  ]
}
```

### 5. Netlify 部署

#### 创建 _redirects 文件

在 `public` 目录下创建 `_redirects` 文件：

```
/api/* https://your-api-domain.com/:splat 200
/* /index.html 200
```

#### 部署

1. 将代码推送到 GitHub
2. 在 Netlify 中连接 GitHub 仓库
3. 设置构建命令：`npm run build`
4. 设置发布目录：`dist`

## 环境变量配置

### 开发环境

创建 `.env.development` 文件：

```env
VITE_API_BASE_URL=http://localhost:8080/api
VITE_APP_TITLE=个人博客 - 开发环境
```

### 生产环境

创建 `.env.production` 文件：

```env
VITE_API_BASE_URL=https://your-api-domain.com/api
VITE_APP_TITLE=个人博客
```

## 性能优化

### 1. 代码分割

项目已配置路由级别的代码分割：

```typescript
const routes = [
  {
    path: '/',
    component: () => import('@/views/Home.vue')
  }
]
```

### 2. 静态资源优化

- 图片压缩和 WebP 格式
- CSS 和 JS 文件压缩
- Gzip 压缩

### 3. CDN 配置

可以将静态资源部署到 CDN：

```typescript
// vite.config.ts
export default defineConfig({
  build: {
    rollupOptions: {
      external: ['vue', 'vue-router'],
      output: {
        globals: {
          vue: 'Vue',
          'vue-router': 'VueRouter'
        }
      }
    }
  }
})
```

## 监控和日志

### 1. 错误监控

可以集成 Sentry 进行错误监控：

```bash
npm install @sentry/vue @sentry/tracing
```

### 2. 访问统计

可以集成 Google Analytics 或百度统计。

## 安全配置

### 1. HTTPS 配置

使用 Let's Encrypt 免费 SSL 证书：

```bash
sudo certbot --nginx -d your-domain.com
```

### 2. 安全头配置

在 Nginx 中添加安全头：

```nginx
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```
