# Vue3 个人博客

基于 Vue3 + TypeScript + Vite 构建的现代化个人博客前端项目。

## 特性

- ⚡️ **Vue 3** - 使用最新的 Vue 3 Composition API
- 🔥 **TypeScript** - 完整的 TypeScript 支持
- 📦 **Vite** - 快速的构建工具
- 🎨 **Element Plus** - 优雅的 UI 组件库
- 🗂 **文件路由** - 基于文件系统的路由
- 📱 **响应式设计** - 完美适配移动端
- 🔍 **搜索功能** - 支持文章搜索和筛选
- 💬 **评论系统** - 完整的评论功能
- 📊 **状态管理** - 使用 Pinia 进行状态管理

## 快速开始

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 项目结构

```
src/
├── api/              # API 接口
├── components/       # 组件
│   ├── layout/      # 布局组件
│   ├── article/     # 文章组件
│   └── comment/     # 评论组件
├── layouts/         # 页面布局
├── stores/          # Pinia 状态管理
├── styles/          # 样式文件
├── types/           # TypeScript 类型定义
├── views/           # 页面组件
└── router/          # 路由配置
```

## 技术栈

- **前端框架**: Vue 3
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP 客户端**: Axios
- **Markdown 解析**: Marked
- **代码高亮**: Highlight.js
- **时间处理**: Day.js

## 浏览器支持

现代浏览器和 IE11+。

## 许可证

[MIT](./LICENSE) License © 2024
