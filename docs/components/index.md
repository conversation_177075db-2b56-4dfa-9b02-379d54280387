# 组件文档

本项目采用组件化开发，所有组件都使用 Vue 3 Composition API 和 TypeScript 编写。

## 组件分类

### 布局组件

负责页面整体布局和导航的组件：

- [HeaderComponent](./layout/header.md) - 页面头部组件
- [SidebarComponent](./layout/sidebar.md) - 侧边栏组件  
- [FooterComponent](./layout/footer.md) - 页面底部组件

### 文章组件

处理文章展示和交互的组件：

- [ArticleCard](./article/article-card.md) - 文章卡片组件
- [ArticleDetail](./article/article-detail.md) - 文章详情组件

### 评论组件

处理评论功能的组件：

- [CommentSection](./comment/comment-section.md) - 评论区域组件
- [CommentItem](./comment/comment-item.md) - 单条评论组件

## 组件开发规范

### 1. 文件命名

- 组件文件使用 PascalCase 命名，如 `ArticleCard.vue`
- 组件目录按功能分类，如 `components/article/`

### 2. 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 导入依赖
import { ref, computed } from 'vue'

// 定义 Props 接口
interface Props {
  // props 定义
}

// 定义 Emits 接口
interface Emits {
  // emits 定义
}

// 使用 defineProps 和 defineEmits
const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const data = ref()

// 计算属性
const computed = computed(() => {
  // 计算逻辑
})

// 方法
const method = () => {
  // 方法实现
}
</script>

<style scoped>
/* 组件样式 */
</style>
```

### 3. TypeScript 支持

- 所有组件都使用 TypeScript
- Props 和 Emits 都要定义接口
- 使用 `defineProps<Props>()` 和 `defineEmits<Emits>()`

### 4. 样式规范

- 使用 `scoped` 样式避免样式污染
- 支持响应式设计，使用媒体查询适配移动端
- 使用 CSS 变量定义主题色彩

### 5. 可访问性

- 为交互元素添加适当的 ARIA 属性
- 确保键盘导航支持
- 提供合适的语义化标签

## 通用工具

### 类型定义

项目中的通用类型定义在 `src/types/index.ts` 中：

```typescript
// 文章类型
interface Article {
  id: number
  title: string
  content: string
  // ...
}

// 分类类型
interface Category {
  id: number
  name: string
  // ...
}
```

### 状态管理

使用 Pinia 进行状态管理，Store 文件位于 `src/stores/` 目录：

- `article.ts` - 文章相关状态
- `category.ts` - 分类相关状态
- `tag.ts` - 标签相关状态

### API 服务

API 服务文件位于 `src/api/` 目录，使用 Axios 进行 HTTP 请求：

- `article.ts` - 文章相关 API
- `category.ts` - 分类相关 API
- `tag.ts` - 标签相关 API
- `comment.ts` - 评论相关 API
