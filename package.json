{"name": "vue3-personal-blog", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "setup": "node scripts/setup.js", "docs:dev": "vitepress dev docs", "docs:build": "vitepress build docs", "docs:preview": "vitepress preview docs"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "axios": "^1.6.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.1", "marked": "^9.1.0", "highlight.js": "^11.9.0", "dayjs": "^1.11.10"}, "devDependencies": {"@types/node": "^20.8.0", "@vitejs/plugin-vue": "^4.4.0", "typescript": "^5.2.0", "vue-tsc": "^1.8.0", "vite": "^4.4.5", "vitepress": "^1.0.0", "unplugin-auto-import": "^0.16.0", "unplugin-vue-components": "^0.25.0"}}