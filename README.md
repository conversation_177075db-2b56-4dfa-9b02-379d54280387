# Vue3 个人博客

基于 Vue3 + TypeScript + Vite 构建的现代化个人博客前端项目。

## ✨ 特性

- ⚡️ **Vue 3** - 使用最新的 Vue 3 Composition API
- 🔥 **TypeScript** - 完整的 TypeScript 支持
- 📦 **Vite** - 快速的构建工具和开发服务器
- 🎨 **Element Plus** - 优雅的 UI 组件库
- 🗂 **Vue Router** - 官方路由管理器
- 📊 **Pinia** - 新一代状态管理库
- 📱 **响应式设计** - 完美适配移动端和桌面端
- 🔍 **搜索功能** - 支持文章搜索和分类筛选
- 💬 **评论系统** - 完整的评论和回复功能
- 🎯 **代码高亮** - 支持多种编程语言语法高亮
- 📝 **Markdown** - 支持 Markdown 格式文章渲染

## 🚀 快速开始

### 环境要求

- Node.js >= 16
- npm >= 7

### 安装依赖

```bash
npm install
```

### 开发模式

```bash
npm run dev
```

访问 http://localhost:3000

### 构建生产版本

```bash
npm run build
```

### 预览生产版本

```bash
npm run preview
```

## 📁 项目结构

```
src/
├── api/              # API 接口
│   ├── index.ts     # Axios 配置
│   ├── article.ts   # 文章接口
│   ├── category.ts  # 分类接口
│   ├── tag.ts       # 标签接口
│   └── comment.ts   # 评论接口
├── components/       # 组件
│   ├── layout/      # 布局组件
│   ├── article/     # 文章组件
│   └── comment/     # 评论组件
├── layouts/         # 页面布局
├── stores/          # Pinia 状态管理
├── styles/          # 样式文件
├── types/           # TypeScript 类型定义
├── views/           # 页面组件
├── router/          # 路由配置
└── main.ts          # 应用入口
```

## 🛠 技术栈

- **前端框架**: Vue 3
- **开发语言**: TypeScript
- **构建工具**: Vite
- **UI 组件库**: Element Plus
- **状态管理**: Pinia
- **路由管理**: Vue Router
- **HTTP 客户端**: Axios
- **Markdown 解析**: Marked
- **代码高亮**: Highlight.js
- **时间处理**: Day.js

## 📖 文档

- [组件文档](./docs/components/)
- [API 接口文档](./docs/api/)
- [部署指南](./docs/deployment/)

### 生成文档

```bash
# 开发模式
npm run docs:dev

# 构建文档
npm run docs:build

# 预览文档
npm run docs:preview
```

## 🔧 配置

### 环境变量

创建 `.env.local` 文件：

```env
# API 基础地址
VITE_API_BASE_URL=http://localhost:8080/api

# 应用标题
VITE_APP_TITLE=个人博客
```

### 代理配置

开发环境下，API 请求会自动代理到后端服务器。可以在 `vite.config.ts` 中修改代理配置：

```typescript
server: {
  proxy: {
    '/api': {
      target: 'http://localhost:8080',
      changeOrigin: true,
      rewrite: (path) => path.replace(/^\/api/, '')
    }
  }
}
```

## 🎯 功能特性

### 首页
- 轮播图展示特色文章
- 文章列表展示
- 分类筛选
- 分页功能

### 文章详情
- Markdown 渲染
- 代码语法高亮
- 文章点赞
- 社交分享
- 相关文章推荐

### 搜索功能
- 关键词搜索
- 分类筛选
- 标签筛选
- 搜索结果分页

### 评论系统
- 发表评论
- 回复评论
- 评论嵌套显示
- 评论审核

### 侧边栏
- 个人信息展示
- 热门文章
- 热门标签
- 最新评论

## 🚀 部署

### Nginx 部署

```bash
# 构建项目
npm run build

# 复制文件到服务器
scp -r dist/* user@server:/var/www/blog/

# 配置 Nginx
# 参考 docs/deployment/index.md
```

### Docker 部署

```bash
# 构建镜像
docker build -t vue3-blog .

# 运行容器
docker run -d -p 80:80 vue3-blog
```

### Vercel 部署

```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

[MIT](./LICENSE) License © 2024

## 🙏 致谢

- [Vue.js](https://vuejs.org/) - 渐进式 JavaScript 框架
- [Element Plus](https://element-plus.org/) - Vue 3 组件库
- [Vite](https://vitejs.dev/) - 下一代前端构建工具
- [TypeScript](https://www.typescriptlang.org/) - JavaScript 的超集
